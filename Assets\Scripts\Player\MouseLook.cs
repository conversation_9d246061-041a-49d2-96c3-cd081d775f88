using UnityEngine;
using UnityEngine.InputSystem;

public class MouseLook : MonoBehaviour
{
    [Header("鼠标灵敏度")]
    [SerializeField] private float mouseSensitivity = 100f;

    [Header("视角限制")]
    [SerializeField] private float maxLookAngle = 80f;

    [Header("组件引用")]
    [SerializeField] private Transform playerBody;
    [SerializeField] private Camera playerCamera;

    // 视角变量
    private float xRotation = 0f;
    private Vector2 lookInput;

    private void Awake()
    {
        // 如果没有指定摄像机，尝试获取当前对象的摄像机
        if (playerCamera == null)
            playerCamera = GetComponent<Camera>();

        // 如果没有指定玩家身体，使用父对象
        if (playerBody == null && transform.parent != null)
            playerBody = transform.parent;
    }

    private void Start()
    {
        // 锁定并隐藏鼠标光标
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }
    
    private void Update()
    {
        // 处理鼠标视角
        HandleMouseLook();

        // 检测ESC键解锁鼠标（调试用）
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            ToggleCursorLock();
        }
    }

    private void HandleMouseLook()
    {
        // 获取鼠标输入并应用灵敏度
        float mouseX = lookInput.x * mouseSensitivity * Time.deltaTime;
        float mouseY = lookInput.y * mouseSensitivity * Time.deltaTime;

        // 水平旋转（左右转动玩家身体）
        if (playerBody != null)
        {
            playerBody.Rotate(Vector3.up * mouseX);
        }

        // 垂直旋转（上下转动摄像机）
        xRotation -= mouseY;
        xRotation = Mathf.Clamp(xRotation, -maxLookAngle, maxLookAngle);

        // 应用垂直旋转到摄像机
        if (playerCamera != null)
        {
            playerCamera.transform.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
        }
        else
        {
            // 如果没有摄像机引用，旋转当前对象
            transform.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
        }
    }

    // Input System 回调方法
    public void OnLook(InputAction.CallbackContext context)
    {
        lookInput = context.ReadValue<Vector2>();
    }
    
    private void ToggleCursorLock()
    {
        if (Cursor.lockState == CursorLockMode.Locked)
        {
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }
        else
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }
    }
    
    // 公共方法：设置鼠标灵敏度
    public void SetMouseSensitivity(float sensitivity)
    {
        mouseSensitivity = sensitivity;
    }
    
    // 公共方法：获取当前鼠标灵敏度
    public float GetMouseSensitivity()
    {
        return mouseSensitivity;
    }
    
    // 公共方法：重置视角
    public void ResetLook()
    {
        xRotation = 0f;
        if (playerCamera != null)
        {
            playerCamera.transform.localRotation = Quaternion.identity;
        }
        else
        {
            transform.localRotation = Quaternion.identity;
        }
    }
}
