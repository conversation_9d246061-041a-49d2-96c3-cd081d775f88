using UnityEngine;
using UnityEngine.InputSystem;

[RequireComponent(typeof(CharacterController))]
public class PlayerMovement : MonoBehaviour
{
    [Header("移动设置")]
    [SerializeField] private float walkSpeed = 5f;
    [SerializeField] private float sprintSpeed = 8f;
    [SerializeField] private float jumpHeight = 2f;
    [SerializeField] private float gravity = -9.81f;

    [Header("地面检测")]
    [SerializeField] private Transform groundCheck;
    [SerializeField] private float groundDistance = 0.4f;
    [SerializeField] private LayerMask groundMask = 1;

    [Header("蹲下设置")]
    [SerializeField] private float crouchHeight = 1f;
    [SerializeField] private float standHeight = 2f;
    [SerializeField] private float crouchSpeed = 2f;

    // 组件引用
    private CharacterController controller;
    private PlayerInput playerInput;

    // 移动变量
    private Vector3 velocity;
    private bool isGrounded;
    private bool isSprinting;
    private bool isCrouching;

    // 输入变量
    private Vector2 moveInput;
    private bool jumpPressed;

    private void Awake()
    {
        // 获取组件
        controller = GetComponent<CharacterController>();
        playerInput = GetComponent<PlayerInput>();

        // 如果没有PlayerInput组件，添加一个
        if (playerInput == null)
        {
            playerInput = gameObject.AddComponent<PlayerInput>();
        }
    }

    private void Start()
    {
        // 设置初始高度
        controller.height = standHeight;
    }
    
    private void Update()
    {
        // 地面检测
        GroundCheck();

        // 处理移动
        HandleMovement();

        // 处理跳跃
        HandleJump();

        // 处理蹲下
        HandleCrouch();

        // 应用重力
        ApplyGravity();

        // 移动角色
        controller.Move(velocity * Time.deltaTime);

        // 重置跳跃输入
        jumpPressed = false;
    }

    private void GroundCheck()
    {
        // 检测是否在地面上
        if (groundCheck != null)
        {
            isGrounded = Physics.CheckSphere(groundCheck.position, groundDistance, groundMask);
        }
        else
        {
            // 如果没有设置groundCheck，使用简单的地面检测
            isGrounded = controller.isGrounded;
        }

        // 如果在地面上且向下速度小于0，重置Y轴速度
        if (isGrounded && velocity.y < 0)
        {
            velocity.y = -2f; // 小的负值确保角色贴地
        }
    }

    private void HandleMovement()
    {
        // 获取移动方向（相对于角色朝向）
        Vector3 move = transform.right * moveInput.x + transform.forward * moveInput.y;

        // 确定当前速度
        float currentSpeed = GetCurrentSpeed();

        // 应用移动
        controller.Move(move * currentSpeed * Time.deltaTime);
    }

    private float GetCurrentSpeed()
    {
        if (isCrouching)
            return crouchSpeed;
        else if (isSprinting && !isCrouching)
            return sprintSpeed;
        else
            return walkSpeed;
    }

    private void HandleJump()
    {
        if (jumpPressed && isGrounded)
        {
            // 计算跳跃速度
            velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);
        }
    }

    private void HandleCrouch()
    {
        if (isCrouching)
        {
            // 蹲下
            controller.height = crouchHeight;
        }
        else
        {
            // 站立
            controller.height = standHeight;
        }
    }

    private void ApplyGravity()
    {
        // 应用重力
        velocity.y += gravity * Time.deltaTime;
    }

    // Input System 回调方法
    public void OnMove(InputAction.CallbackContext context)
    {
        moveInput = context.ReadValue<Vector2>();
    }

    public void OnJump(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            jumpPressed = true;
        }
    }

    public void OnSprint(InputAction.CallbackContext context)
    {
        isSprinting = context.ReadValueAsButton();
    }

    public void OnCrouch(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            isCrouching = !isCrouching; // 切换蹲下状态
        }
    }
    
    // 在Scene视图中绘制地面检测球体
    private void OnDrawGizmosSelected()
    {
        if (groundCheck != null)
        {
            Gizmos.color = isGrounded ? Color.green : Color.red;
            Gizmos.DrawWireSphere(groundCheck.position, groundDistance);
        }
    }
}
