{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 17020, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 17020, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 17020, "tid": 355, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 17020, "tid": 355, "ts": 1754044787986154, "dur": 502, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 17020, "tid": 355, "ts": 1754044787989038, "dur": 704, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 17020, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786917494, "dur": 24110, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786941605, "dur": 1040560, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786941609, "dur": 46, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786941660, "dur": 30085, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786971751, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786971793, "dur": 5, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786971800, "dur": 2530, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974334, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974384, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974386, "dur": 43, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974433, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974435, "dur": 39, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974477, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974478, "dur": 40, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974521, "dur": 35, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974559, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974561, "dur": 33, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974595, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974597, "dur": 26, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974626, "dur": 37, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974667, "dur": 29, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974698, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974699, "dur": 45, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974748, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974778, "dur": 35, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974817, "dur": 46, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974867, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974868, "dur": 34, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974904, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974906, "dur": 27, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974934, "dur": 27, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786974964, "dur": 34, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975000, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975002, "dur": 38, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975041, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975042, "dur": 24, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975069, "dur": 36, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975107, "dur": 23, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975131, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975133, "dur": 24, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975160, "dur": 22, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975184, "dur": 23, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975208, "dur": 29, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975240, "dur": 26, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975269, "dur": 26, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975297, "dur": 22, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975321, "dur": 25, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975348, "dur": 25, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975376, "dur": 24, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975402, "dur": 23, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975427, "dur": 29, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975457, "dur": 24, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975483, "dur": 25, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975510, "dur": 25, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975536, "dur": 15, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975553, "dur": 22, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975577, "dur": 29, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975608, "dur": 24, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975634, "dur": 21, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975656, "dur": 19, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975677, "dur": 20, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975699, "dur": 18, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975720, "dur": 20, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975742, "dur": 28, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975771, "dur": 20, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975793, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975815, "dur": 21, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975838, "dur": 17, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975857, "dur": 26, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975886, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975918, "dur": 18, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975938, "dur": 20, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975960, "dur": 20, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786975981, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976004, "dur": 20, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976026, "dur": 19, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976047, "dur": 18, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976066, "dur": 20, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976089, "dur": 24, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976115, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976138, "dur": 19, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976158, "dur": 31, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976191, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976212, "dur": 21, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976235, "dur": 20, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976256, "dur": 17, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976275, "dur": 18, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976295, "dur": 19, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976316, "dur": 19, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976337, "dur": 18, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976356, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976380, "dur": 23, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976404, "dur": 24, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976432, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976433, "dur": 46, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976482, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976483, "dur": 33, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976518, "dur": 31, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976551, "dur": 23, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976576, "dur": 25, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976603, "dur": 26, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976631, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976632, "dur": 31, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976665, "dur": 20, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976687, "dur": 22, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976711, "dur": 22, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976735, "dur": 17, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976753, "dur": 20, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976775, "dur": 21, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976797, "dur": 87, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976887, "dur": 33, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976921, "dur": 23, "ph": "X", "name": "ReadAsync 1331", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976946, "dur": 28, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976976, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786976979, "dur": 32, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977014, "dur": 21, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977037, "dur": 17, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977055, "dur": 20, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977077, "dur": 20, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977099, "dur": 23, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977124, "dur": 28, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977153, "dur": 24, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977179, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977181, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977204, "dur": 19, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977225, "dur": 21, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977247, "dur": 38, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977287, "dur": 20, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977309, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977331, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977333, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977350, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977373, "dur": 21, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977396, "dur": 20, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977418, "dur": 49, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977468, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977492, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977493, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977516, "dur": 20, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977537, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977561, "dur": 20, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977583, "dur": 20, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977604, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977626, "dur": 18, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977645, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977646, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977668, "dur": 25, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977695, "dur": 40, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977737, "dur": 28, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977767, "dur": 21, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977790, "dur": 45, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977839, "dur": 34, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977875, "dur": 22, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977899, "dur": 54, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977954, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786977977, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978000, "dur": 20, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978021, "dur": 21, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978044, "dur": 21, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978068, "dur": 18, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978087, "dur": 19, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978107, "dur": 21, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978129, "dur": 18, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978149, "dur": 19, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978170, "dur": 21, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978193, "dur": 23, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978217, "dur": 21, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978239, "dur": 22, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978263, "dur": 21, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978285, "dur": 20, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978307, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978329, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978353, "dur": 21, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978375, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978398, "dur": 20, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978420, "dur": 20, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978442, "dur": 20, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978465, "dur": 18, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978485, "dur": 21, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978507, "dur": 24, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978533, "dur": 23, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978558, "dur": 22, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978582, "dur": 29, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978613, "dur": 43, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978660, "dur": 42, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978704, "dur": 1, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978705, "dur": 31, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978739, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978740, "dur": 30, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978772, "dur": 23, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978798, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978799, "dur": 31, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978834, "dur": 31, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978867, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978868, "dur": 34, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978904, "dur": 24, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978931, "dur": 23, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978955, "dur": 20, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786978977, "dur": 24, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979003, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979005, "dur": 31, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979038, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979040, "dur": 30, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979074, "dur": 32, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979109, "dur": 27, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979139, "dur": 46, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979188, "dur": 25, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979216, "dur": 19, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979237, "dur": 28, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979266, "dur": 25, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979293, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979294, "dur": 35, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979333, "dur": 26, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979361, "dur": 140, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979503, "dur": 23, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979529, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979552, "dur": 26, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979581, "dur": 189, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979772, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979793, "dur": 24, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979818, "dur": 26, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979846, "dur": 23, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979872, "dur": 23, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979897, "dur": 17, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979916, "dur": 20, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979940, "dur": 24, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979966, "dur": 20, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786979987, "dur": 15, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980004, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980026, "dur": 24, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980053, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980055, "dur": 34, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980091, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980093, "dur": 25, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980120, "dur": 18, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980140, "dur": 21, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980162, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980185, "dur": 23, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980211, "dur": 21, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980234, "dur": 21, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980257, "dur": 17, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980276, "dur": 20, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980298, "dur": 21, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980321, "dur": 21, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980344, "dur": 20, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980366, "dur": 22, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980389, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980411, "dur": 25, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980437, "dur": 20, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980459, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980460, "dur": 20, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980482, "dur": 19, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980503, "dur": 21, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980525, "dur": 20, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980547, "dur": 19, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980567, "dur": 23, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980592, "dur": 35, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980629, "dur": 20, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980650, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980651, "dur": 20, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980673, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980697, "dur": 18, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980717, "dur": 19, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980738, "dur": 17, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980757, "dur": 19, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980779, "dur": 19, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980799, "dur": 18, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980819, "dur": 19, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980840, "dur": 19, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980861, "dur": 18, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980880, "dur": 18, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980899, "dur": 21, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980923, "dur": 17, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980941, "dur": 18, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786980961, "dur": 105, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981068, "dur": 20, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981090, "dur": 55, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981147, "dur": 26, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981175, "dur": 18, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981195, "dur": 22, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981219, "dur": 21, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981242, "dur": 19, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981263, "dur": 18, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981283, "dur": 20, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981304, "dur": 18, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981324, "dur": 18, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981344, "dur": 22, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981367, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981387, "dur": 18, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981407, "dur": 21, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981430, "dur": 17, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981448, "dur": 19, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981468, "dur": 20, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981490, "dur": 18, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981510, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981532, "dur": 20, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981554, "dur": 19, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981574, "dur": 23, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981599, "dur": 27, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981628, "dur": 24, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981655, "dur": 25, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981683, "dur": 23, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981708, "dur": 49, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981759, "dur": 27, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981788, "dur": 21, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981813, "dur": 27, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981841, "dur": 28, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981871, "dur": 48, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981921, "dur": 26, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981949, "dur": 21, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981971, "dur": 21, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786981994, "dur": 80, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982077, "dur": 28, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982108, "dur": 23, "ph": "X", "name": "ReadAsync 1005", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982133, "dur": 22, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982157, "dur": 18, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982177, "dur": 20, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982199, "dur": 19, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982219, "dur": 19, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982240, "dur": 21, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982263, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982284, "dur": 20, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982306, "dur": 19, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982327, "dur": 17, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982345, "dur": 19, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982366, "dur": 22, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982389, "dur": 22, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982412, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982414, "dur": 32, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982449, "dur": 29, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982480, "dur": 18, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982500, "dur": 19, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982521, "dur": 20, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982542, "dur": 19, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982563, "dur": 18, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982583, "dur": 25, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982609, "dur": 20, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982631, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982652, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982675, "dur": 23, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982700, "dur": 18, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982719, "dur": 19, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982741, "dur": 17, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982759, "dur": 19, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982780, "dur": 22, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982804, "dur": 20, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982826, "dur": 22, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982850, "dur": 21, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982872, "dur": 17, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982891, "dur": 19, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982912, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982913, "dur": 20, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982935, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982954, "dur": 20, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982976, "dur": 20, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786982997, "dur": 18, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983017, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983038, "dur": 20, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983060, "dur": 19, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983081, "dur": 19, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983103, "dur": 20, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983125, "dur": 18, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983145, "dur": 19, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983166, "dur": 19, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983187, "dur": 19, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983208, "dur": 19, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983228, "dur": 20, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983250, "dur": 19, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983271, "dur": 22, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983294, "dur": 20, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983315, "dur": 18, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983335, "dur": 19, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983355, "dur": 19, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983376, "dur": 18, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983395, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983416, "dur": 22, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983440, "dur": 21, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983463, "dur": 19, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983484, "dur": 26, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983512, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983530, "dur": 19, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983550, "dur": 21, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983573, "dur": 17, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983591, "dur": 34, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983627, "dur": 22, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983652, "dur": 17, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983670, "dur": 26, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983698, "dur": 20, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983719, "dur": 18, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983739, "dur": 23, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983764, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983786, "dur": 20, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983809, "dur": 19, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983830, "dur": 19, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983851, "dur": 18, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983871, "dur": 20, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983892, "dur": 19, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983914, "dur": 17, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983932, "dur": 19, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983954, "dur": 20, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983976, "dur": 17, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786983995, "dur": 18, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786984015, "dur": 19, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786984036, "dur": 833, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786984874, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786984908, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786984935, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985076, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985115, "dur": 202, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985321, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985357, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985415, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985452, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985599, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985642, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985644, "dur": 36, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985684, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985717, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985760, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985794, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985796, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985830, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985870, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786985900, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986007, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986040, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986070, "dur": 179, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986254, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986290, "dur": 117, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986412, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986447, "dur": 114, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986566, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986600, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986636, "dur": 54, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986694, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986725, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986842, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986870, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986911, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986947, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786986986, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987034, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987064, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987126, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987163, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987197, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987252, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987293, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987348, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987376, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987378, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987420, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987503, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987527, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987567, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987606, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987608, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987636, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987670, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987701, "dur": 160, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987865, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987909, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786987960, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988049, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988079, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988105, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988106, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988150, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988180, "dur": 28, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988211, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988240, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988335, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988364, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988400, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988403, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988485, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988516, "dur": 239, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988759, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988797, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988799, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988831, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988864, "dur": 126, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786988993, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989026, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989152, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989182, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989215, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989216, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989245, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989279, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989338, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989372, "dur": 196, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989572, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989632, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989633, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989697, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989739, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989740, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989805, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989834, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989836, "dur": 71, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989910, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989947, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989949, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786989977, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990036, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990074, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990105, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990136, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990165, "dur": 48, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990216, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990243, "dur": 52, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990299, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990336, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990366, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990400, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990427, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990475, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990510, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990573, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990607, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990641, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990678, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990700, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990750, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990753, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990786, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990815, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990841, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990864, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990947, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786990973, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991007, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991037, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991059, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991185, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991219, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991251, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991273, "dur": 57, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991334, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991354, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991402, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991405, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991426, "dur": 6, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991434, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991463, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991493, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991520, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991544, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991573, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991602, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991673, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991706, "dur": 142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991850, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991884, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991916, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991918, "dur": 79, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786991999, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992028, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992059, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992103, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992133, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992219, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992259, "dur": 157, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992420, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992445, "dur": 51, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992499, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992528, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992587, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992618, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992619, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992650, "dur": 84, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992737, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992771, "dur": 164, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992938, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786992960, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993029, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993064, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993065, "dur": 41, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993109, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993135, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993163, "dur": 26, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993193, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993241, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993271, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993406, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993431, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993545, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993575, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993607, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993692, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993730, "dur": 40, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993773, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993775, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993804, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993892, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786993922, "dur": 163, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994086, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994119, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994236, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994270, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994306, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994343, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994345, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994383, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994385, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994416, "dur": 24, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994445, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994475, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994585, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994619, "dur": 218, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994841, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994876, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994877, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994919, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994921, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994956, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786994992, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786995020, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786995048, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786995102, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786995130, "dur": 535, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786995668, "dur": 43, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786995714, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786995716, "dur": 244, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786995965, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786995992, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786995994, "dur": 602, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786996600, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786996628, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786996630, "dur": 143, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786996778, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786996810, "dur": 1513, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786998325, "dur": 55, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786998383, "dur": 387, "ph": "X", "name": "ProcessMessages 158", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044786998773, "dur": 17762, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787016540, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787016584, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787016621, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787016653, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787016716, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787016747, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787016801, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787016831, "dur": 5154, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787021989, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787022020, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787022113, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787022144, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787022291, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787022326, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787022328, "dur": 5583, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787027915, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787027949, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787028038, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787028072, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787028075, "dur": 81, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787028158, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787028182, "dur": 389, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787028576, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787028608, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787028701, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787028741, "dur": 409, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787029153, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787029184, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787029269, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787029301, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787029345, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787029373, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787029374, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787029413, "dur": 271, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787029688, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787029721, "dur": 707, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030432, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030465, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030513, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030548, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030581, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030583, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030653, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030681, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030715, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030749, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030772, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030796, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030816, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030850, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030853, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030883, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030912, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030965, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030996, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787030998, "dur": 28, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031029, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031078, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031098, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031131, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031156, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031158, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031192, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031254, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031284, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031287, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031332, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031368, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031370, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031396, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031496, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031536, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031572, "dur": 56, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031631, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031690, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031692, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031722, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031724, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031750, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031752, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031790, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031825, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031855, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031858, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031885, "dur": 21, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031910, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031936, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031959, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787031985, "dur": 234, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787032223, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787032255, "dur": 1, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787032257, "dur": 81, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787032342, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787032370, "dur": 272, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787032647, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787032700, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787032702, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787032753, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787032790, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787032823, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787032856, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787032931, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787032933, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787032974, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033049, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033087, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033122, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033150, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033153, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033188, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033189, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033228, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033230, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033260, "dur": 22, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033284, "dur": 1, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033286, "dur": 65, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033356, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033384, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033424, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033451, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033480, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033511, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033543, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033567, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033650, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033683, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033745, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033779, "dur": 1, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033782, "dur": 37, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787033822, "dur": 1112, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787034939, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787034975, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787034976, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787035042, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787035075, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787035102, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787035127, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787035226, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787035257, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787035281, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787035313, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787035338, "dur": 425, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787035768, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787035815, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787035901, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787035936, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787035937, "dur": 179, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787036122, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787036169, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787036224, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787036258, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787036298, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787036300, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787036460, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787036498, "dur": 517, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787037019, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787037058, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787037137, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787037171, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787037212, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787037257, "dur": 1, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787037259, "dur": 35, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787037298, "dur": 313, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787037614, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787037654, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787037756, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787037788, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787037791, "dur": 121201, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787159001, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787159005, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787159062, "dur": 36, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787159100, "dur": 25, "ph": "X", "name": "ProcessMessages 35", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787159127, "dur": 645, "ph": "X", "name": "ReadAsync 35", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787159776, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787159803, "dur": 10, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787159814, "dur": 723, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787160541, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787160577, "dur": 11, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787160589, "dur": 372, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787160966, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787161007, "dur": 13, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787161022, "dur": 261, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787161287, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787161317, "dur": 9, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787161327, "dur": 199, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787161530, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787161572, "dur": 9, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787161582, "dur": 139, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787161725, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787161753, "dur": 11, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787161765, "dur": 25, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787161793, "dur": 9, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787161803, "dur": 360, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787162167, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787162196, "dur": 9, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787162206, "dur": 2263, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787164473, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787164514, "dur": 9, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787164524, "dur": 1229, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787165757, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787165784, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787165786, "dur": 97904, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787263696, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787263699, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787263741, "dur": 18, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787263761, "dur": 570, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787264334, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787264369, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787264372, "dur": 687015, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787951394, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787951397, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787951438, "dur": 22, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787951461, "dur": 15757, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787967223, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754044787967251, "dur": 14908, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 17020, "tid": 355, "ts": 1754044787989745, "dur": 799, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 17020, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 17020, "tid": 17179869184, "ts": 1754044786917471, "dur": 3, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 17020, "tid": 17179869184, "ts": 1754044786917474, "dur": 24116, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 17020, "tid": 17179869184, "ts": 1754044786941591, "dur": 32, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 17020, "tid": 355, "ts": 1754044787990546, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 17020, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 17020, "tid": 1, "ts": 1754044786533945, "dur": 3511, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17020, "tid": 1, "ts": 1754044786537458, "dur": 36582, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17020, "tid": 1, "ts": 1754044786574044, "dur": 28726, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 17020, "tid": 355, "ts": 1754044787990551, "dur": 2, "ph": "X", "name": "", "args": {}}, {"pid": 17020, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786532781, "dur": 20105, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786552887, "dur": 84652, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786553627, "dur": 1901, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786555533, "dur": 890, "ph": "X", "name": "ProcessMessages 4691", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556425, "dur": 166, "ph": "X", "name": "ReadAsync 4691", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556593, "dur": 9, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556603, "dur": 32, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556639, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556641, "dur": 40, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556684, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556686, "dur": 47, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556736, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556739, "dur": 33, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556773, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556775, "dur": 26, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556803, "dur": 27, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556833, "dur": 24, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556859, "dur": 30, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556891, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556893, "dur": 40, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556937, "dur": 40, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786556980, "dur": 27, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557010, "dur": 27, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557039, "dur": 24, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557066, "dur": 35, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557104, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557106, "dur": 35, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557144, "dur": 2, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557147, "dur": 36, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557186, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557187, "dur": 33, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557223, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557224, "dur": 26, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557252, "dur": 27, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557282, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557306, "dur": 24, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557333, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557357, "dur": 36, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557395, "dur": 27, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557425, "dur": 34, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557463, "dur": 25, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557490, "dur": 23, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557515, "dur": 21, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557538, "dur": 21, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557561, "dur": 24, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557587, "dur": 22, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557612, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557634, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557657, "dur": 18, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557678, "dur": 20, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557700, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557722, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557744, "dur": 1, "ph": "X", "name": "ProcessMessages 49", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557746, "dur": 19, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557767, "dur": 21, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557790, "dur": 20, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557812, "dur": 24, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557837, "dur": 22, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557862, "dur": 20, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557883, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557885, "dur": 21, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557907, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557908, "dur": 20, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557930, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557954, "dur": 20, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786557976, "dur": 45, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558024, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558060, "dur": 24, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558088, "dur": 25, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558115, "dur": 20, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558137, "dur": 20, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558160, "dur": 21, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558184, "dur": 24, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558210, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558233, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558256, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558258, "dur": 24, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558284, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558307, "dur": 22, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558331, "dur": 38, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558372, "dur": 26, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558399, "dur": 26, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558428, "dur": 23, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558454, "dur": 24, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558479, "dur": 28, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558510, "dur": 22, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558534, "dur": 20, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558556, "dur": 20, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558578, "dur": 21, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558601, "dur": 20, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558622, "dur": 21, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558645, "dur": 24, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558671, "dur": 20, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558694, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558717, "dur": 23, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558743, "dur": 22, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558766, "dur": 20, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558787, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558789, "dur": 33, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558823, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558825, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558848, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558870, "dur": 19, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558891, "dur": 21, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558915, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558938, "dur": 21, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558962, "dur": 22, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786558986, "dur": 22, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559010, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559035, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559058, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559083, "dur": 21, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559106, "dur": 22, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559130, "dur": 19, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559153, "dur": 20, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559175, "dur": 19, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559197, "dur": 27, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559225, "dur": 21, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559248, "dur": 21, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559271, "dur": 21, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559293, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559317, "dur": 22, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559340, "dur": 32, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559375, "dur": 35, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559413, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559415, "dur": 30, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559447, "dur": 31, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559483, "dur": 25, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559509, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559511, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559534, "dur": 22, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559558, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559579, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559602, "dur": 20, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559625, "dur": 24, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559651, "dur": 25, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559677, "dur": 24, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559703, "dur": 20, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559725, "dur": 26, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559753, "dur": 21, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559776, "dur": 21, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559799, "dur": 22, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559823, "dur": 19, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559844, "dur": 20, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559866, "dur": 20, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559888, "dur": 21, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559910, "dur": 20, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559932, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559954, "dur": 21, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786559978, "dur": 20, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560000, "dur": 149, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560152, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560177, "dur": 30, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560209, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560212, "dur": 28, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560243, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560245, "dur": 28, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560275, "dur": 27, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560305, "dur": 26, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560334, "dur": 24, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560361, "dur": 26, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560391, "dur": 26, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560419, "dur": 24, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560447, "dur": 25, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560473, "dur": 23, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560498, "dur": 21, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560521, "dur": 27, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560550, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560573, "dur": 20, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560595, "dur": 26, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786560623, "dur": 24, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786564851, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786564853, "dur": 179, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565034, "dur": 10, "ph": "X", "name": "ProcessMessages 20562", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565044, "dur": 24, "ph": "X", "name": "ReadAsync 20562", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565071, "dur": 27, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565101, "dur": 28, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565130, "dur": 21, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565154, "dur": 20, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565175, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565197, "dur": 21, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565221, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565245, "dur": 20, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565267, "dur": 20, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565289, "dur": 21, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565311, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565334, "dur": 20, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565356, "dur": 21, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565379, "dur": 57, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565472, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565474, "dur": 216, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786565921, "dur": 24, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786566046, "dur": 252, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786566298, "dur": 5, "ph": "X", "name": "ProcessMessages 11368", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786566347, "dur": 101, "ph": "X", "name": "ReadAsync 11368", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786566543, "dur": 1, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786566649, "dur": 271, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786567079, "dur": 4, "ph": "X", "name": "ProcessMessages 4880", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786569989, "dur": 60, "ph": "X", "name": "ReadAsync 4880", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570052, "dur": 208, "ph": "X", "name": "ProcessMessages 3485", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570262, "dur": 40, "ph": "X", "name": "ReadAsync 3485", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570305, "dur": 26, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570334, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570336, "dur": 52, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570392, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570431, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570433, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570489, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570528, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570530, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570563, "dur": 27, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570594, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570702, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570738, "dur": 56, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570797, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570855, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570882, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570915, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570943, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786570945, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571008, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571040, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571168, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571203, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571269, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571307, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571348, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571379, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571417, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571419, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571477, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571516, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571555, "dur": 96, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571654, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571679, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571805, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571841, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571843, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571880, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571883, "dur": 26, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571913, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571937, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571992, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786571994, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572026, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572055, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572106, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572142, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572191, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572218, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572252, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572299, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572334, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572363, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572421, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572457, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572458, "dur": 135, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572597, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572713, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572742, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572743, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572778, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572803, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572832, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786572864, "dur": 148, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573017, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573053, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573055, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573080, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573082, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573115, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573176, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573211, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573338, "dur": 109, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573451, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573495, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573496, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573525, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573553, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573555, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573593, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573594, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573625, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573706, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573734, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573800, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786573828, "dur": 187, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574019, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574053, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574084, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574179, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574214, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574215, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574254, "dur": 33, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574292, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574326, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574357, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574437, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574465, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574581, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574612, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574639, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574683, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574709, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574803, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574834, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574868, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574892, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574924, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574958, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786574995, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575043, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575071, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575128, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575154, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575185, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575211, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575236, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575265, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575354, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575401, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575404, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575443, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575477, "dur": 26, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575506, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575533, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575561, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575602, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575631, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575716, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575747, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575771, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575796, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575860, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575890, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786575984, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576010, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576012, "dur": 72, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576088, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576120, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576146, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576213, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576244, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576272, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576380, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576411, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576439, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576506, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576509, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576547, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576549, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576616, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576645, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576680, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576681, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576715, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576747, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576776, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576842, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576870, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576903, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576964, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786576994, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577016, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577112, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577147, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577176, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577208, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577244, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577276, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577310, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577313, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577371, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577401, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577553, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577594, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577621, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577690, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577721, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577776, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577810, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577842, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577890, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577892, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577922, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786577948, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786578097, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786578123, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786578176, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786578207, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786578247, "dur": 127, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786578377, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786578403, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786578426, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786578448, "dur": 21854, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786600306, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786600308, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786600336, "dur": 204, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786600544, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786600570, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786600603, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786600633, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786600760, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786600785, "dur": 6026, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786606815, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786606844, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786606911, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786606944, "dur": 120, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786607069, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786607095, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786607118, "dur": 6339, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786613461, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786613506, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786613634, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786613663, "dur": 163, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754044786613828, "dur": 23358, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 17020, "tid": 355, "ts": 1754044787990554, "dur": 358, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 17020, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 17020, "tid": 8589934592, "ts": 1754044786531133, "dur": 71664, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 17020, "tid": 8589934592, "ts": 1754044786602800, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 17020, "tid": 8589934592, "ts": 1754044786602804, "dur": 1182, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 17020, "tid": 355, "ts": 1754044787990913, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 17020, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 17020, "tid": 4294967296, "ts": 1754044786513728, "dur": 124397, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 17020, "tid": 4294967296, "ts": 1754044786517486, "dur": 8632, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 17020, "tid": 4294967296, "ts": 1754044786638200, "dur": 273352, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 17020, "tid": 4294967296, "ts": 1754044786911705, "dur": 1070479, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 17020, "tid": 4294967296, "ts": 1754044786911799, "dur": 5652, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 17020, "tid": 4294967296, "ts": 1754044787982352, "dur": 2257, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 17020, "tid": 4294967296, "ts": 1754044787983819, "dur": 23, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 17020, "tid": 4294967296, "ts": 1754044787984612, "dur": 6, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 17020, "tid": 355, "ts": 1754044787990917, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754044786942018, "dur": 32084, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044786974105, "dur": 106, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044786974279, "dur": 414, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044786978999, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_E668CD773429A040.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754044786979784, "dur": 133, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754044786981375, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754044786982260, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754044786974716, "dur": 9719, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044786984443, "dur": 981775, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044787966219, "dur": 299, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044787966566, "dur": 109, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044787967538, "dur": 71, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044787967624, "dur": 10215, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754044786974816, "dur": 9633, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786990824, "dur": 361, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/UnityEditor/6000.1.14f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1754044786984455, "dur": 6730, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786991190, "dur": 646, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786991841, "dur": 596, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786992441, "dur": 542, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786993008, "dur": 519, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786993533, "dur": 571, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786994116, "dur": 618, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786994777, "dur": 560, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786995342, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786995712, "dur": 2915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786998627, "dur": 2386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787001014, "dur": 2879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787003894, "dur": 2326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787006220, "dur": 2383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787008603, "dur": 2277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787010880, "dur": 2772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787013653, "dur": 2075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787015728, "dur": 2832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787018561, "dur": 2265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787020827, "dur": 3096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787023923, "dur": 3647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787027570, "dur": 789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787028359, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787029008, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787029140, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754044787029243, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787029666, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754044787030010, "dur": 1043, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787031068, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787031213, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787031288, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787031388, "dur": 522, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787031915, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787032020, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787032197, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787032289, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787032371, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787032744, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787033064, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787033201, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787033335, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787033579, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787033960, "dur": 1409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787035372, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787035624, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1754044787035679, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787036177, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787036323, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787036528, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787036639, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754044787036726, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787036854, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754044787037134, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044787037428, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.ref.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754044787037426, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_A1A44354E0B583A8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754044787037683, "dur": 123269, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_A1A44354E0B583A8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754044787161069, "dur": 805245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786974889, "dur": 9581, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786984476, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_7A4B826FC3B9A562.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786984886, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786985251, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_A3598586C48DAB89.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786985462, "dur": 706, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786986174, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_8BE78E10F7CFA16B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786986733, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786987109, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_B337F2B9F8637F8B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786987242, "dur": 552, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786987799, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_C6B01611FE9BE340.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786987972, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786988485, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_2E0DBB4CDB1259B3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786988772, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786988888, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_4A13D76A06B15607.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786989221, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786989624, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_23DE2903C5ADDDF2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786990045, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786990284, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_23DE2903C5ADDDF2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786990343, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_7DC540854D1BBB01.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786990437, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786990824, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786991125, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754044786991191, "dur": 635, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786991835, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786992399, "dur": 505, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786992926, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786993443, "dur": 656, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786994111, "dur": 616, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786994749, "dur": 542, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786997016, "dur": 794, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754044786997811, "dur": 2966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787000777, "dur": 2547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787003324, "dur": 2418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787005742, "dur": 2240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787007982, "dur": 2351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787010334, "dur": 2206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787012540, "dur": 2616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787015157, "dur": 2088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787017245, "dur": 2461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787019706, "dur": 2175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787021881, "dur": 2354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787024236, "dur": 3466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787027702, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787028359, "dur": 653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787029012, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787029153, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044787029261, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787029774, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754044787030137, "dur": 1219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044787031377, "dur": 558, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Performance.Profile-Analyzer.Editor.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754044787031376, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_65A192712782EB2B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044787032013, "dur": 128169, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_65A192712782EB2B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044787160282, "dur": 805937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786974978, "dur": 9525, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786984510, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_5DC9A836C807FAF2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786985283, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786985469, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_7575D6D4E1069A15.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786985546, "dur": 643, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786986193, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EDFFA85DD2BD1FB2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786986782, "dur": 480, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786987267, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_805911B8F5770868.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786987436, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786987971, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_7887D396BEE6166C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786988232, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786988534, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_1DAA338F289F96AC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786988970, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786989250, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_20FB01B34163AD70.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786989578, "dur": 487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786990069, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_27CD92644E7249A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786990213, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786990620, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_220A2C8EABC83166.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786990756, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786991090, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_220A2C8EABC83166.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786991171, "dur": 648, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786991843, "dur": 598, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786992445, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786993009, "dur": 499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786993515, "dur": 611, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786994139, "dur": 618, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786994781, "dur": 606, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786995407, "dur": 487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786995898, "dur": 2832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786998731, "dur": 2998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787001729, "dur": 2527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787004257, "dur": 2564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787006821, "dur": 2410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787009232, "dur": 2507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787011739, "dur": 2565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787014304, "dur": 2211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787016515, "dur": 2152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787018667, "dur": 2335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787021003, "dur": 2247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787023251, "dur": 2180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787025431, "dur": 2907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787028341, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787028453, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787029014, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787029155, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044787029258, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787029780, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754044787030162, "dur": 1318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044787031488, "dur": 633, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualStudio.Editor.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754044787031487, "dur": 636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044787032408, "dur": 129283, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044787161784, "dur": 804521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786974847, "dur": 9613, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786984469, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_FC130522DD32B68F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786984885, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786985136, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_610A92F577D74C23.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786985332, "dur": 640, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786985982, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1CFBC647265B7D61.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786986173, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786986274, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_17A99C1F49AC544C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786986837, "dur": 593, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786987436, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AC1B2F844B840E57.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786987541, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786987994, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_F184BD0C8FDF7284.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786988414, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786988621, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_AB176FE8EEF474FE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786989162, "dur": 404, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786989572, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_9FDB4296FD5402ED.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786989741, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786990201, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786990343, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786990556, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786990707, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786991096, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786991155, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786991734, "dur": 24970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754044787016705, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787016937, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787017027, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787017119, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787017206, "dur": 2061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787019267, "dur": 2188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787021455, "dur": 2375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787023830, "dur": 3431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787027262, "dur": 1105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787028368, "dur": 653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787029021, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787029163, "dur": 1682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787030848, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787030959, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787031068, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787031211, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787031290, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787031393, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787031752, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787032028, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787032374, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787032621, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787033049, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787033148, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787033238, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787033510, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787033851, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787034037, "dur": 1350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787035390, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787035691, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787036174, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787036320, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787036528, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787036678, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787037447, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044787038025, "dur": 928196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786974921, "dur": 9558, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786984486, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_184A04796BB6AF30.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786984915, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786985290, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_B1046D03C7B936D0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786985400, "dur": 683, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786986087, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_59BEFA3164F84BCE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786986673, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786986833, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_521EBAF2E01427CC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786987021, "dur": 626, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786987652, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_FCA4F4639D8CB7B1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786987799, "dur": 667, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786988473, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_61B9003D7A1B656E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786988880, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786989158, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_8C95FB58FA13BAB6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786989341, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786989740, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_B359FB15FAFC6B04.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786990101, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786990499, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786990609, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786990978, "dur": 458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786991482, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786992074, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786992634, "dur": 505, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786993142, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786993650, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786994165, "dur": 622, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786994811, "dur": 577, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786995408, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786995790, "dur": 3199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786998989, "dur": 2791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787001780, "dur": 2368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787004148, "dur": 2363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787006511, "dur": 2436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787008948, "dur": 2389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787011337, "dur": 2324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787013662, "dur": 2097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787015760, "dur": 3059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787018819, "dur": 2347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787021167, "dur": 2336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787023504, "dur": 2175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787025679, "dur": 2683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787028362, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787029010, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787029148, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044787029264, "dur": 810, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787030084, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754044787030352, "dur": 1212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787031570, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787031798, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044787031908, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787032312, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754044787032994, "dur": 626, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044787033626, "dur": 505, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.Editor.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754044787033625, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0D3D3C0B73557612.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044787034213, "dur": 130681, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0D3D3C0B73557612.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044787166024, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754044787164989, "dur": 1152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044787166206, "dur": 97877, "ph": "X", "name": "MovedFromExtractor-Combine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044787264541, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754044787264175, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754044787264952, "dur": 686827, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754044786974965, "dur": 9526, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786984499, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C9FC54791F8F8DD6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786985026, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786985326, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_816918F4C6F4A52A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786985462, "dur": 685, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786986171, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CB0C3B3019515683.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786986714, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786987033, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_F538D3A612872E43.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786987167, "dur": 578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786987753, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_EBEA4291B5996ADA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786987967, "dur": 514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786988490, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_4BCB4A18FFEC0881.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786988884, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786989155, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A2ECA603E2EEF8E4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786989325, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786989666, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_23CB3E72B95C6251.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786990111, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786990443, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786990554, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786990919, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786991388, "dur": 558, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786991954, "dur": 546, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786992511, "dur": 531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786993046, "dur": 509, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786993559, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786994157, "dur": 651, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786994834, "dur": 589, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786995430, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786995884, "dur": 3446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786999330, "dur": 2492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787001822, "dur": 2275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787004097, "dur": 2521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787006618, "dur": 2115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787008733, "dur": 2471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787011204, "dur": 2488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787013692, "dur": 2368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787016061, "dur": 2376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787018438, "dur": 2057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787020495, "dur": 2098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787022595, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787022710, "dur": 2281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787025113, "dur": 3226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787028340, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787029006, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787029142, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044787029249, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787029670, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754044787029989, "dur": 840, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787030844, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787030913, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044787031000, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787031409, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754044787031906, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044787032421, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Editor.ref.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754044787032342, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044787032759, "dur": 129388, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044787162241, "dur": 804020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786975013, "dur": 9500, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786984520, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_C9F914A74E2B16B8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786985315, "dur": 662, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786985993, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_E0A580A67B9BA514.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786986428, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786986645, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B40E848BC76ACCC7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786986951, "dur": 595, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786987551, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_347DC0FF07300132.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786987786, "dur": 571, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786988361, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_51F15FE6673D9942.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786988540, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786988772, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_48A7353BAEF13316.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786989199, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786989602, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_8B112C68541FCD06.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786989916, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786990205, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786990339, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786990552, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786990661, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786991057, "dur": 613, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786991678, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786992283, "dur": 531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786992843, "dur": 581, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786993431, "dur": 498, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786993936, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786994299, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786994866, "dur": 567, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786995456, "dur": 441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786995902, "dur": 2765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786998667, "dur": 2914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787001582, "dur": 2674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787004256, "dur": 2435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787006691, "dur": 2133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787008825, "dur": 2262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787011088, "dur": 1966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787013055, "dur": 2809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787015865, "dur": 1903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787017768, "dur": 1940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787019709, "dur": 2291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787022021, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787022600, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787022720, "dur": 2159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787024879, "dur": 2904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787027784, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787028355, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787029019, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787029172, "dur": 1671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787030845, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787030954, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787031069, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787031210, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787031305, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787031378, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787031741, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044787031888, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787032274, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754044787032591, "dur": 728, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787033329, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787033567, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787033886, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787034063, "dur": 1274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787035340, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787035515, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787036176, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787036534, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787037426, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787037649, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044787038021, "dur": 928204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786975051, "dur": 9475, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786984535, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_F279AD6DB3287D52.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786985360, "dur": 620, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786985991, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_FADE1643CA5C8800.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786986278, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786986403, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_ED2D3EB1C97F3A5F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786986835, "dur": 503, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786987343, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_CAEDB152A2C80D92.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786987447, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786987974, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_F4C63E5BAEA27403.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786988297, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786988563, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_B9E05D4615C858EB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786989024, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786989241, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_3D9F6B68BD00EB1C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786989567, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786989949, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9582B39E166EA023.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786990160, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786990534, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786990661, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786991058, "dur": 590, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786991654, "dur": 623, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786992286, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786992862, "dur": 571, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786993438, "dur": 648, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786994092, "dur": 540, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786994654, "dur": 571, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786995239, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786995569, "dur": 3308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786998878, "dur": 2792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787001670, "dur": 2580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787004250, "dur": 2344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787006594, "dur": 2268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787008862, "dur": 2429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787011291, "dur": 2432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787013724, "dur": 2241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787015966, "dur": 2342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787018308, "dur": 2084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787020393, "dur": 2255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787022648, "dur": 2261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787024910, "dur": 2206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787027116, "dur": 1238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787028354, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787029006, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787029144, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044787029253, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787029689, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754044787030058, "dur": 1217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787031287, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787031385, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044787031458, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787032008, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044787032086, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754044787032467, "dur": 567, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044787033048, "dur": 485, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.ref.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754044787033046, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_F09CF4826CE11CD9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044787033614, "dur": 128567, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_F09CF4826CE11CD9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044787162267, "dur": 803985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786975095, "dur": 9445, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786984545, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_612DCBB3052D864A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786985352, "dur": 795, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786986163, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_8B4EA0E95E9C463B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786986695, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786986958, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_E742B87826F4607B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786987109, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786987669, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_2B292166361825BD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786987840, "dur": 625, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786988470, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_90D0F47A19362C10.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786988624, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786988778, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_574D2D579249D9A8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786989221, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786989616, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_13697A979EB0EF82.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786990022, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786990237, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_13697A979EB0EF82.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786990357, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786990559, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786990705, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786990886, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786991253, "dur": 634, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786991892, "dur": 553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786992461, "dur": 559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786993025, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786993556, "dur": 590, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786994163, "dur": 652, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786994842, "dur": 595, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786997793, "dur": 894, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754044786998688, "dur": 3372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787002060, "dur": 2471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787004531, "dur": 2555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787007086, "dur": 2236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787009323, "dur": 2284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787011608, "dur": 2701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787014309, "dur": 2082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787016392, "dur": 2031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787018424, "dur": 2298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787020722, "dur": 2678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787023401, "dur": 2115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787025516, "dur": 2832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787028348, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787029017, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787029158, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044787029260, "dur": 474, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787029750, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754044787030119, "dur": 1257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787031385, "dur": 644, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Rider.Editor.ref.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754044787031384, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044787032035, "dur": 70, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044787032177, "dur": 127201, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044787159498, "dur": 806725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786975130, "dur": 9416, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786984553, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_36C5F3CCEBE3CD07.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786985320, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786985814, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_9919525412EAD2E7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786985956, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786986211, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_339B7DA446DBACC5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786986783, "dur": 446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786987238, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_7FF844E13374B2E6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786987404, "dur": 491, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786987901, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_37301B1867509DEE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786988011, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786988482, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E3304A047EE08E0B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786988882, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786989161, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_092522C3C590FAD2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786989372, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786989744, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_94172ADB7F54A7D8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786990091, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786990364, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_AC876CD17C0FF1C1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786990437, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786990772, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786991143, "dur": 664, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786991850, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786992455, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786993022, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786993573, "dur": 590, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786994201, "dur": 639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786996115, "dur": 1099, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754044786997216, "dur": 3064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787000280, "dur": 2508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787002788, "dur": 2643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787005431, "dur": 2234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787007665, "dur": 2178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787009844, "dur": 2075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787011919, "dur": 2644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787014564, "dur": 2102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787016666, "dur": 2139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787018805, "dur": 2347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787021152, "dur": 2099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787023252, "dur": 2341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787025593, "dur": 2763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787028356, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787029011, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787029157, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044787029261, "dur": 484, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787029751, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754044787030052, "dur": 1059, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044787031121, "dur": 410, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.EditorCoroutines.Editor.ref.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754044787031120, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_34ED10A34098B2DB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044787031811, "dur": 130761, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_34ED10A34098B2DB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044787162645, "dur": 803591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786975193, "dur": 9366, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786984566, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_2202EAFAEF1B6C1F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786985322, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786985717, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_C048E937E9A421B8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786985804, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786986208, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_42F8665F9413CA2D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786986810, "dur": 495, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786987311, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_1F33D875853821BA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786987444, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786987978, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_14260DC7C6A62D51.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786988372, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786988553, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_00A3E5D2DC969885.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786989020, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786989242, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_941F941CDF3DCEAF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786989570, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786990053, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_19B07DA70A32A185.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786990213, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786990721, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786991004, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786991588, "dur": 657, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786992251, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786992826, "dur": 513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786993348, "dur": 454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786993808, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786994180, "dur": 658, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786994864, "dur": 574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786995463, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786998445, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\treeview\\ItemGui\\TimelineMarkerClusterGUI.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754044786995910, "dur": 3862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786999773, "dur": 3000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787002773, "dur": 2274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787005047, "dur": 2233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787007281, "dur": 2195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787009476, "dur": 2407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787011883, "dur": 2637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787014520, "dur": 2109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787016629, "dur": 2136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787018766, "dur": 2087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787020854, "dur": 2834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787023689, "dur": 2056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787025745, "dur": 2613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787028358, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787029004, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787029145, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044787029250, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787029762, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754044787030154, "dur": 1228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787031390, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787031483, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044787031577, "dur": 490, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787032068, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044787032119, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754044787032551, "dur": 505, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044787033063, "dur": 530, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.ref.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754044787033061, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044787033702, "dur": 127669, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044787161481, "dur": 804798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786975224, "dur": 9346, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786984576, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_D9842110F5874C86.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786985359, "dur": 640, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786986007, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_825F83770C6FBB2D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786986368, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786986451, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DB41EC179EB56DBA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786986839, "dur": 609, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786987452, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FCA09B517E031ABB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786987558, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786988019, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8FAB570D0B1C8DCD.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786988415, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786988582, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_968B718F611B56F9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786989149, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 17540***********, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_386811273654939A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786989581, "dur": 531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786990118, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_CB3E21EA52206828.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786990215, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786990635, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_A45CE29EBBBE7D91.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786990768, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786991094, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_A45CE29EBBBE7D91.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786991159, "dur": 646, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786991819, "dur": 623, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786992462, "dur": 543, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786993010, "dur": 537, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786993551, "dur": 572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786994136, "dur": 599, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786994760, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786995308, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786995704, "dur": 2781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786998486, "dur": 3025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787001511, "dur": 2452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787003963, "dur": 2179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787006143, "dur": 2398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787008542, "dur": 2138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787010681, "dur": 2101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787012782, "dur": 2794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787015576, "dur": 2536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787018112, "dur": 2408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787020520, "dur": 2706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787023226, "dur": 2372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787025598, "dur": 2739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787028340, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787028450, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787029015, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787029161, "dur": 1687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787030848, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787031069, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787031129, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787031234, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787031289, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787031392, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787031736, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787032034, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044787032134, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787032374, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754044787032668, "dur": 763, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787033442, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787033830, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787033950, "dur": 1440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787035390, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787036178, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787036530, "dur": 898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787037445, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787037662, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787038021, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044787038152, "dur": 928074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786975264, "dur": 9316, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786984585, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_61250575CD81DD6E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044786985330, "dur": 646, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786986004, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_B89CB73E89E58DB9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044786986429, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786986648, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_98ABF5EF82F6C903.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044786986974, "dur": 544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786987525, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B16C90EDBDE4DF78.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044786987623, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786988070, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_66EAD504DFBBEDF3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044786988436, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786988617, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_885799F0F6E77EBF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044786989159, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786989550, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A9C25E502CB87508.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044786989663, "dur": 505, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786990175, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_3861702E21F7C7AB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044786990289, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786990715, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786990976, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786991397, "dur": 600, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786992001, "dur": 612, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786992627, "dur": 505, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786993138, "dur": 504, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786993648, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786994162, "dur": 646, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786994838, "dur": 585, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786995428, "dur": 453, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786995887, "dur": 2789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786998677, "dur": 2907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787001584, "dur": 2681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787004265, "dur": 2421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787006687, "dur": 2281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787008968, "dur": 2570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787011538, "dur": 2590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787014129, "dur": 2202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787016331, "dur": 2202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787018534, "dur": 2430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787020965, "dur": 2029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787022994, "dur": 2284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787025279, "dur": 3061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787028340, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787028999, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787029143, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044787029254, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787029685, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754044787030053, "dur": 1143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787031210, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787031308, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787031374, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787031657, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787031896, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787032021, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787032246, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787032353, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787033060, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787033214, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787033445, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787033753, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787033919, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787033990, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044787034079, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787034206, "dur": 931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754044787035137, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787035360, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787035499, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044787035597, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787035731, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754044787036177, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787036528, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787036632, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044787036723, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787036851, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754044787037180, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787037444, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787037532, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044787037596, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787037672, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754044787037836, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044787038017, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Shared.Editor.ref.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754044787038016, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_9669F6EBFF6DFFB7.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044787038217, "dur": 123716, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_9669F6EBFF6DFFB7.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044787162170, "dur": 804101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786975300, "dur": 9293, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786984602, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_9C0B6EFD81BDE75D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786985389, "dur": 674, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786986068, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_57991EBFA1A26EC2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786986637, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786986810, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_054B6FF5C640A1AD.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786987021, "dur": 548, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786987573, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_6A2198A2D22CCFAF.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786987791, "dur": 560, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786988357, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_0E440929134BF079.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786988535, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786988769, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_00BEFECA1F6D79F8.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786989202, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786989595, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_FD894A3BC08E0559.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786989849, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786990178, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_4B5328702BD9392E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786990292, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786990732, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786990785, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786991111, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786991182, "dur": 30770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754044787021953, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787022391, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787022510, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044787022592, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787022682, "dur": 5461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754044787028143, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787028359, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787028441, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044787028502, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787028564, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754044787028777, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787028999, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787029158, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044787029238, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787029558, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754044787030328, "dur": 1189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787031524, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787031660, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044787031862, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787032235, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754044787033102, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787033690, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787033969, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044787034060, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787034157, "dur": 942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754044787035100, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787035339, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787035441, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044787035531, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787035675, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754044787035967, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787036174, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787036300, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787036529, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787036682, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787037425, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787037629, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044787038026, "dur": 928190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786975335, "dur": 9269, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786984609, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_9E563BCFB42744CB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786985328, "dur": 694, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786986027, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_CA47B9E89BF3744B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786986479, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786986679, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_0A3076E440EF1041.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786987018, "dur": 545, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786987568, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_30E83EFD1EA5B11D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786987787, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786988280, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_1DFFFA9F10D99920.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786988529, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786988729, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_1E7AA3722A1C93AD.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786989180, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786989592, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_86405D0799B6BB11.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786989922, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786990210, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_86405D0799B6BB11.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786990328, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786990546, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786990675, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786991068, "dur": 603, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786991682, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786992293, "dur": 603, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786992915, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786993440, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786993990, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786994495, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786994987, "dur": 507, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786995518, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786995918, "dur": 2787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786998706, "dur": 2919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787001625, "dur": 2520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787004145, "dur": 2798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787006943, "dur": 2295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787009239, "dur": 2560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787011799, "dur": 2651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787014450, "dur": 2208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787016658, "dur": 2495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787019153, "dur": 2207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787021361, "dur": 2337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787023698, "dur": 1983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787025681, "dur": 2668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787028349, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787029004, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787029167, "dur": 1680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787030847, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787031071, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787031126, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787031209, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787031287, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787031406, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787031744, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044787031899, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787032238, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754044787032728, "dur": 815, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787033550, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787033926, "dur": 1425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787035354, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787035656, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787036177, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787036529, "dur": 896, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787037449, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787037669, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787038019, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044787038149, "dur": 928066, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786975368, "dur": 9244, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786984612, "dur": 737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_F22C2CAEEE3C250C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044786985350, "dur": 753, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786986109, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_E9025F1EBE6B906B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044786986694, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786987010, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_8E5076AB386A4F5F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044786987139, "dur": 603, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786987749, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_86942DFBA173BD2F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044786987962, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786988496, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_6FFB81672CC7662A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044786988899, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786989235, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_A982C66D2D348ED7.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044786989523, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786989938, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E6F1BAA280F85427.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044786990179, "dur": 431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786990618, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786990744, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786991055, "dur": 529, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786991591, "dur": 684, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786992280, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786992836, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786993435, "dur": 660, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786994105, "dur": 595, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786994724, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786996364, "dur": 1081, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754044786999130, "dur": 632, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\Utils\\ManipulatorsUtils.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754044786997446, "dur": 3972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787001418, "dur": 2609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787004027, "dur": 2351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787006378, "dur": 2328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787008706, "dur": 2288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787010995, "dur": 2116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787013111, "dur": 2624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787015735, "dur": 2907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787018642, "dur": 2214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787020857, "dur": 2015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787022873, "dur": 2232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787025111, "dur": 3230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787028342, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787028999, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787029151, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044787029256, "dur": 487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787029747, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754044787030441, "dur": 1238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787031685, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044787031939, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787032293, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754044787032722, "dur": 777, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044787033507, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.ForUI.ref.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754044787033505, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_0478B67D1094CE70.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044787033844, "dur": 128290, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_0478B67D1094CE70.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044787162205, "dur": 804089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044787980634, "dur": 1522, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1754044786717137, "dur": 177489, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754044786717849, "dur": 44148, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754044786859179, "dur": 4687, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754044786863867, "dur": 30753, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754044786865082, "dur": 24238, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754044786899992, "dur": 994, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754044786899721, "dur": 1401, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754044786551270, "dur": 2804, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044786554083, "dur": 355, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044786554524, "dur": 418, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044786555405, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_61250575CD81DD6E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754044786555564, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_816918F4C6F4A52A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754044786556892, "dur": 172, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_6FFB81672CC7662A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754044786562705, "dur": 2791, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754044786566679, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754044786567164, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754044786554966, "dur": 12447, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044786567418, "dur": 41251, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044786608670, "dur": 677, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044786609363, "dur": 812, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044786610255, "dur": 3707, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044786614028, "dur": 54, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044786614096, "dur": 1747, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754044786555090, "dur": 12338, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786567439, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_23CB3E72B95C6251.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754044786567878, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786568160, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_5DC9A836C807FAF2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754044786568355, "dur": 791, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786569187, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1CFBC647265B7D61.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754044786569813, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786570183, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_98ABF5EF82F6C903.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754044786570300, "dur": 548, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786570858, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_6A2198A2D22CCFAF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754044786571342, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786571623, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_61B9003D7A1B656E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754044786571856, "dur": 511, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786572373, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_941F941CDF3DCEAF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754044786572683, "dur": 490, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786573181, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754044786573408, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786573805, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786574170, "dur": 635, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786574810, "dur": 606, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786575427, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786575997, "dur": 566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786576574, "dur": 575, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786577159, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786577714, "dur": 566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786578310, "dur": 473, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786578790, "dur": 3087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786581877, "dur": 2613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786584491, "dur": 2704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786587196, "dur": 2410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786589606, "dur": 2486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786592092, "dur": 2405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786594498, "dur": 2512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786597010, "dur": 2353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786599363, "dur": 2959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786602322, "dur": 2612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786604934, "dur": 2140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754044786607074, "dur": 2224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786555166, "dur": 12285, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786567458, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_94172ADB7F54A7D8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786567878, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786568205, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_C9F914A74E2B16B8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786568357, "dur": 793, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786569159, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_9919525412EAD2E7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786569680, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786569822, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_ED2D3EB1C97F3A5F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786570100, "dur": 648, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786570773, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FCA09B517E031ABB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786570978, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786571345, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_66EAD504DFBBEDF3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786571729, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786572256, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_1E7AA3722A1C93AD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786572403, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786572771, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786572921, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786573228, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754044786573423, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786573896, "dur": 572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786574479, "dur": 604, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786575087, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786575659, "dur": 541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786576206, "dur": 623, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786576835, "dur": 515, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786577354, "dur": 646, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786578033, "dur": 512, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786578550, "dur": 3235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786581785, "dur": 2937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786584723, "dur": 2450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786587173, "dur": 2510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786589683, "dur": 2243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786591926, "dur": 2075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786594001, "dur": 2233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786596234, "dur": 3222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786599456, "dur": 2554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786602010, "dur": 2716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786604726, "dur": 3348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754044786608074, "dur": 2056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786555188, "dur": 12277, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786567474, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E6F1BAA280F85427.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786568051, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786568292, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_612DCBB3052D864A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786568391, "dur": 782, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786569192, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_E0A580A67B9BA514.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786569814, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786570297, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_054B6FF5C640A1AD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786570405, "dur": 453, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786570863, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_2B292166361825BD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786571328, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786571462, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_90D0F47A19362C10.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786571808, "dur": 487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786572302, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_574D2D579249D9A8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786572600, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786572898, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786573173, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754044786573420, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786573932, "dur": 589, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786574526, "dur": 609, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786575141, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786575721, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786576327, "dur": 560, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786576896, "dur": 574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786577484, "dur": 580, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786578085, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786578631, "dur": 3211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786581842, "dur": 2844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786584686, "dur": 2534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786587220, "dur": 2322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786589542, "dur": 2517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786592059, "dur": 2227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786594377, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Analysis\\IGraphElementAnalysis.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754044786594286, "dur": 3017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786597303, "dur": 2486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786599789, "dur": 2240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786602029, "dur": 2679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754044786604708, "dur": 3950, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786555134, "dur": 12306, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786567445, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_B359FB15FAFC6B04.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786567878, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786568240, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_F279AD6DB3287D52.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786568365, "dur": 770, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786569140, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_CA47B9E89BF3744B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786569566, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786569674, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_42F8665F9413CA2D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786570097, "dur": 650, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786570755, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_CAEDB152A2C80D92.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786570908, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786571310, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8FAB570D0B1C8DCD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786571646, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786572005, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_885799F0F6E77EBF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786572279, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786572684, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_FD894A3BC08E0559.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754044786573073, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786573525, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786573936, "dur": 570, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786574510, "dur": 578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786575092, "dur": 575, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786575681, "dur": 566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786576255, "dur": 580, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786576839, "dur": 576, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786577421, "dur": 580, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786578042, "dur": 507, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786578553, "dur": 3162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786581716, "dur": 2897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786584613, "dur": 2745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786587358, "dur": 2639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786589998, "dur": 2082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786592081, "dur": 2504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786594585, "dur": 2691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786597277, "dur": 2935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786600213, "dur": 2211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786602424, "dur": 2426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786604850, "dur": 2549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786607402, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754044786607530, "dur": 2228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786555249, "dur": 12250, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786567504, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_19B07DA70A32A185.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786568262, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786568496, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_2202EAFAEF1B6C1F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786568593, "dur": 589, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786569187, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_FADE1643CA5C8800.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786569817, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786570157, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DB41EC179EB56DBA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786570273, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786570796, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B16C90EDBDE4DF78.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786571076, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786571352, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_1DFFFA9F10D99920.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786571730, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786572113, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_AB176FE8EEF474FE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786572279, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786572656, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_86405D0799B6BB11.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754044786573072, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786573530, "dur": 474, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786574018, "dur": 625, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786574674, "dur": 581, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786575274, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786575858, "dur": 578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786576441, "dur": 510, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786576957, "dur": 603, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786577567, "dur": 572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786578162, "dur": 514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786578681, "dur": 3368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786582049, "dur": 2840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786584889, "dur": 2534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786587424, "dur": 2275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786589699, "dur": 2354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786592053, "dur": 2553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786594606, "dur": 2673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786597279, "dur": 2377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786599656, "dur": 2162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786601818, "dur": 2530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786604348, "dur": 3310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754044786607658, "dur": 1969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786555236, "dur": 12243, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786567489, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9582B39E166EA023.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786568258, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786568423, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_36C5F3CCEBE3CD07.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786568497, "dur": 651, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786569157, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_C048E937E9A421B8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786569673, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786569818, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_17A99C1F49AC544C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786570104, "dur": 617, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786570727, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_1F33D875853821BA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786570825, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786571210, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_14260DC7C6A62D51.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786571498, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786571929, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_8C95FB58FA13BAB6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786572252, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786572653, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_9FDB4296FD5402ED.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754044786573101, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786573511, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786573797, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786574302, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786574900, "dur": 595, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786575511, "dur": 545, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786576067, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786576665, "dur": 548, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786577216, "dur": 540, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786577775, "dur": 587, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786578367, "dur": 3388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786581755, "dur": 2970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786584725, "dur": 2477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786587203, "dur": 2429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786589633, "dur": 2578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786594103, "dur": 533, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Interface\\Colors\\ColorPalette.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754044786592211, "dur": 2535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786594746, "dur": 2807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786597553, "dur": 2120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786599674, "dur": 2323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786601997, "dur": 2244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786604241, "dur": 2445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786606712, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754044786607610, "dur": 2148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786555284, "dur": 12225, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786567515, "dur": 775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_13697A979EB0EF82.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786568291, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786568774, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_61250575CD81DD6E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786568957, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786569211, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_825F83770C6FBB2D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786569818, "dur": 448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786570271, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_0A3076E440EF1041.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786570339, "dur": 507, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786570853, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_30E83EFD1EA5B11D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786571185, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786571396, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_51F15FE6673D9942.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786571800, "dur": 491, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786572298, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_48A7353BAEF13316.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786572521, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786572824, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786573056, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_7DC540854D1BBB01.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754044786573231, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786573529, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786573977, "dur": 652, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786574645, "dur": 606, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786575259, "dur": 545, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786575832, "dur": 591, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786576464, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786577106, "dur": 527, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786577639, "dur": 598, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786578263, "dur": 636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786578902, "dur": 3106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786582008, "dur": 2943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786584952, "dur": 2422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786587374, "dur": 2473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786589847, "dur": 2426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786592273, "dur": 2291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786594565, "dur": 2650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786597215, "dur": 2713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786599929, "dur": 2449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786602379, "dur": 2480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786604860, "dur": 2634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754044786607494, "dur": 2342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786555326, "dur": 12192, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786567524, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_23DE2903C5ADDDF2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786568263, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786568626, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_D9842110F5874C86.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786568704, "dur": 496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786569205, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_B89CB73E89E58DB9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786569819, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786570172, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B40E848BC76ACCC7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786570297, "dur": 497, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786570800, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_347DC0FF07300132.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786571140, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786571383, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_0E440929134BF079.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786571729, "dur": 521, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786572260, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_00BEFECA1F6D79F8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786572393, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786572762, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_8B112C68541FCD06.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786573113, "dur": 412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786573535, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786573615, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786574046, "dur": 32867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754044786606914, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786607273, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786607376, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754044786607470, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754044786607565, "dur": 6103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754044786613669, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786555357, "dur": 12175, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786567538, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_27CD92644E7249A2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786568294, "dur": 809, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786569109, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_B1046D03C7B936D0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786569451, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786569531, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_8BE78E10F7CFA16B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786569986, "dur": 521, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786570516, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_7FF844E13374B2E6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786570752, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786571162, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_37301B1867509DEE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786571398, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786571843, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_6FFB81672CC7662A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786571997, "dur": 446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786572450, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_A982C66D2D348ED7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786572780, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786573210, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754044786573444, "dur": 440, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786573895, "dur": 571, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786574472, "dur": 556, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786575037, "dur": 543, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786575593, "dur": 572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786576177, "dur": 524, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786576711, "dur": 581, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786577303, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786577850, "dur": 544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786578397, "dur": 3281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786581678, "dur": 3440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786585118, "dur": 2412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786587530, "dur": 2578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786590108, "dur": 2535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786592643, "dur": 2313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786594956, "dur": 2485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786597441, "dur": 2477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786599918, "dur": 2321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786602239, "dur": 2477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786604717, "dur": 3178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754044786607895, "dur": 2072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786555398, "dur": 12143, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786567548, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_4B5328702BD9392E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786568295, "dur": 619, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786568919, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_9E563BCFB42744CB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786569157, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786569389, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_59BEFA3164F84BCE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786569853, "dur": 469, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786570327, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_521EBAF2E01427CC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786570406, "dur": 446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786570860, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_FCA4F4639D8CB7B1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786571344, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786571725, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_2E0DBB4CDB1259B3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786571860, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786572333, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A2ECA603E2EEF8E4.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786572635, "dur": 454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786573096, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_AC876CD17C0FF1C1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754044786573251, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786573578, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786574079, "dur": 675, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786574757, "dur": 575, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786575337, "dur": 555, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786575921, "dur": 613, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786576541, "dur": 578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786577124, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786577707, "dur": 578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786578322, "dur": 558, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786578885, "dur": 3332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786582217, "dur": 2929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786585147, "dur": 2717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786587865, "dur": 2408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786590274, "dur": 2342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786592616, "dur": 2495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786595111, "dur": 3471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786598583, "dur": 2299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786600884, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786601003, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754044786601070, "dur": 2372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786603442, "dur": 2476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786605918, "dur": 2446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754044786608365, "dur": 2501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786555424, "dur": 12127, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786567556, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_3861702E21F7C7AB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786568293, "dur": 761, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786569055, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_3861702E21F7C7AB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786569152, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_7575D6D4E1069A15.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786569602, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786569714, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_339B7DA446DBACC5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786570098, "dur": 651, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786570763, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AC1B2F844B840E57.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786570913, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786571306, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_F184BD0C8FDF7284.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786571615, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786571984, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_968B718F611B56F9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786572274, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786572648, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A9C25E502CB87508.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754044786573069, "dur": 423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786573563, "dur": 454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786574027, "dur": 730, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786574769, "dur": 608, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786575389, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786575952, "dur": 606, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786576562, "dur": 560, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786577128, "dur": 580, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786577712, "dur": 574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786578316, "dur": 523, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786578843, "dur": 2986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786581829, "dur": 3148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786584978, "dur": 2781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786587760, "dur": 2304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786590064, "dur": 2274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786592338, "dur": 2227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786594566, "dur": 2844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786597410, "dur": 2060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786599470, "dur": 2526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786601996, "dur": 2116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786604113, "dur": 2220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786606333, "dur": 1885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754044786608218, "dur": 1973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786555487, "dur": 12071, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786567563, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_CB3E21EA52206828.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786568305, "dur": 515, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786568824, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_9C0B6EFD81BDE75D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786569138, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786569386, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_57991EBFA1A26EC2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786569914, "dur": 541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786570460, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_8E5076AB386A4F5F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786570636, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786571020, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_C6B01611FE9BE340.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786571371, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786571807, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_4BCB4A18FFEC0881.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786571924, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786572398, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_092522C3C590FAD2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786572752, "dur": 448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786573207, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754044786573462, "dur": 531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786574010, "dur": 664, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786574700, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786575300, "dur": 558, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786575885, "dur": 559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786576464, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786577072, "dur": 558, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786577635, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786578255, "dur": 598, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786578857, "dur": 3707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786582564, "dur": 2804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786585368, "dur": 2785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786588154, "dur": 2300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786590454, "dur": 2271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786592725, "dur": 2601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786595326, "dur": 2581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786597908, "dur": 2455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786600364, "dur": 2626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786602990, "dur": 2911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786605902, "dur": 2294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754044786608196, "dur": 1915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786555525, "dur": 12042, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786567573, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_FC130522DD32B68F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044786568316, "dur": 616, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786568936, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_F22C2CAEEE3C250C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044786569154, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786569306, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CB0C3B3019515683.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044786569855, "dur": 544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786570405, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_E742B87826F4607B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044786570504, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786570933, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_86942DFBA173BD2F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044786571347, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786571640, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E3304A047EE08E0B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044786571850, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786572305, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_4A13D76A06B15607.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754044786572560, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786572888, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786573121, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786573241, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786573505, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786573658, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786574033, "dur": 723, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786574761, "dur": 648, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786575423, "dur": 561, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786576026, "dur": 570, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786576600, "dur": 591, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786577196, "dur": 546, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786577767, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786578351, "dur": 3331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786581682, "dur": 3203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786584885, "dur": 2725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786587610, "dur": 2484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786590094, "dur": 2930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786593024, "dur": 2952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786595976, "dur": 3043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786599019, "dur": 2803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786601822, "dur": 2268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786604090, "dur": 2702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754044786606792, "dur": 2302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786555575, "dur": 12002, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786567582, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_7A4B826FC3B9A562.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786568362, "dur": 755, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786569121, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_816918F4C6F4A52A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786569512, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786569598, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EDFFA85DD2BD1FB2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786570038, "dur": 574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786570619, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_805911B8F5770868.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786570793, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786571184, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_F4C63E5BAEA27403.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786571437, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786571859, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_00A3E5D2DC969885.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786572078, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786572494, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_20FB01B34163AD70.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786572788, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786573228, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786573446, "dur": 556, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786574009, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754044786574100, "dur": 658, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786574774, "dur": 25658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754044786600433, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786600757, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786601028, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786601218, "dur": 2354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786603573, "dur": 2674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786606247, "dur": 2299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754044786608547, "dur": 2429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786555619, "dur": 11969, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786567596, "dur": 720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_184A04796BB6AF30.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786568317, "dur": 719, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786569043, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_610A92F577D74C23.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786569205, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786569392, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_E9025F1EBE6B906B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786569935, "dur": 544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786570512, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_B337F2B9F8637F8B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786570771, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786571179, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_7887D396BEE6166C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786571442, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786571863, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_B9E05D4615C858EB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786572107, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786572566, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_386811273654939A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786572883, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786573293, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_A45CE29EBBBE7D91.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754044786573464, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786573974, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754044786574030, "dur": 668, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786574704, "dur": 595, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786575304, "dur": 576, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786575908, "dur": 543, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786576455, "dur": 604, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786577066, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786577638, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786578258, "dur": 566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786578827, "dur": 723, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\Sequence\\TrackZoom.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754044786578827, "dur": 4170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786582997, "dur": 2915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786585912, "dur": 2542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786588454, "dur": 2719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786591173, "dur": 2244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786594182, "dur": 699, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Context\\IGraphContext.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754044786593418, "dur": 2913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786596331, "dur": 3063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786599394, "dur": 2917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786602312, "dur": 3048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786605360, "dur": 2180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754044786607541, "dur": 2250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786555679, "dur": 11919, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786567599, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C9FC54791F8F8DD6.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044786568291, "dur": 760, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786569085, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_A3598586C48DAB89.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044786569425, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786569493, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_8B4EA0E95E9C463B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044786569941, "dur": 517, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786570463, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_F538D3A612872E43.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044786570652, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786571010, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_EBEA4291B5996ADA.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044786571386, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786571853, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_1DAA338F289F96AC.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044786572080, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786572467, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_3D9F6B68BD00EB1C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044786572786, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786573240, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786573448, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_220A2C8EABC83166.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754044786573601, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786574004, "dur": 627, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786574646, "dur": 607, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786575266, "dur": 540, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786575840, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786576444, "dur": 545, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786576994, "dur": 587, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786577591, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786578177, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786578690, "dur": 3365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786582055, "dur": 2946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786585001, "dur": 2495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786587496, "dur": 2462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786589958, "dur": 2252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786592210, "dur": 2345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786594556, "dur": 2864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786597421, "dur": 2369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786599791, "dur": 2568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786602359, "dur": 2355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786604715, "dur": 2646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786607364, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754044786607526, "dur": 2086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754044786636204, "dur": 1091, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 17020, "tid": 355, "ts": 1754044787991169, "dur": 1265, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 17020, "tid": 355, "ts": 1754044787994437, "dur": 24, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 17020, "tid": 355, "ts": 1754044787994893, "dur": 11, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 17020, "tid": 355, "ts": 1754044787992547, "dur": 1887, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 17020, "tid": 355, "ts": 1754044787994556, "dur": 337, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 17020, "tid": 355, "ts": 1754044787994989, "dur": 590, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 17020, "tid": 355, "ts": 1754044787988162, "dur": 8046, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}