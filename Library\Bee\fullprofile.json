{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 17020, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 17020, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 17020, "tid": 504, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 17020, "tid": 504, "ts": 1754045722946584, "dur": 592, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 17020, "tid": 504, "ts": 1754045722949652, "dur": 646, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 17020, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722637355, "dur": 21008, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722658364, "dur": 283994, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722658368, "dur": 35, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722658406, "dur": 26241, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722684649, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722684651, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722684690, "dur": 4, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722684696, "dur": 2355, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687055, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687090, "dur": 41, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687135, "dur": 28, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687165, "dur": 22, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687189, "dur": 27, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687219, "dur": 43, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687264, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687266, "dur": 33, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687302, "dur": 32, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687337, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687339, "dur": 45, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687387, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687393, "dur": 46, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687442, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687445, "dur": 37, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687485, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687488, "dur": 38, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687530, "dur": 50, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687582, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687611, "dur": 29, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687643, "dur": 32, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687679, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687681, "dur": 30, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687713, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687736, "dur": 1, "ph": "X", "name": "ProcessMessages 59", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687738, "dur": 37, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687779, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687781, "dur": 37, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687820, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687822, "dur": 62, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687886, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687888, "dur": 29, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687920, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687921, "dur": 46, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687970, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722687973, "dur": 38, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688016, "dur": 39, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688058, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688060, "dur": 31, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688094, "dur": 32, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688130, "dur": 27, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688159, "dur": 35, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688198, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688232, "dur": 26, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688263, "dur": 31, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688296, "dur": 34, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688333, "dur": 31, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688367, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688368, "dur": 113, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688484, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688486, "dur": 23, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688512, "dur": 26, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688540, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688542, "dur": 36, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688581, "dur": 34, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688618, "dur": 36, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688656, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688658, "dur": 23, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688684, "dur": 18, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688704, "dur": 24, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688732, "dur": 26, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688761, "dur": 17, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688780, "dur": 38, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688821, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688823, "dur": 34, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688858, "dur": 27, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688889, "dur": 28, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688919, "dur": 20, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688942, "dur": 24, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722688969, "dur": 32, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689003, "dur": 26, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689031, "dur": 23, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689056, "dur": 23, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689082, "dur": 19, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689103, "dur": 34, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689141, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689176, "dur": 25, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689204, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689205, "dur": 30, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689237, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689239, "dur": 28, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689270, "dur": 26, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689298, "dur": 23, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689323, "dur": 19, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689343, "dur": 21, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689366, "dur": 26, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689395, "dur": 22, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689420, "dur": 22, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689444, "dur": 28, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689474, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689476, "dur": 30, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689510, "dur": 34, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689547, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689548, "dur": 32, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689582, "dur": 44, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689629, "dur": 27, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689659, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689685, "dur": 31, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689718, "dur": 24, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689744, "dur": 22, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689768, "dur": 19, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689790, "dur": 28, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689821, "dur": 26, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689849, "dur": 29, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689881, "dur": 23, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689906, "dur": 19, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689927, "dur": 18, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689947, "dur": 21, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689969, "dur": 23, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722689994, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690016, "dur": 19, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690037, "dur": 16, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690055, "dur": 20, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690077, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690099, "dur": 31, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690133, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690153, "dur": 19, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690173, "dur": 23, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690198, "dur": 41, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690241, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690243, "dur": 38, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690284, "dur": 21, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690307, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690309, "dur": 32, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690345, "dur": 27, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690374, "dur": 24, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690400, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690401, "dur": 26, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690429, "dur": 20, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690452, "dur": 20, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690474, "dur": 28, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690504, "dur": 30, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690536, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690538, "dur": 28, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690568, "dur": 26, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690596, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690598, "dur": 32, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690634, "dur": 23, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690659, "dur": 25, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690686, "dur": 31, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690718, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690719, "dur": 26, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690747, "dur": 21, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690770, "dur": 21, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690793, "dur": 24, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690818, "dur": 22, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690842, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690843, "dur": 23, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690868, "dur": 53, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690925, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690959, "dur": 24, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690985, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722690987, "dur": 26, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691015, "dur": 20, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691038, "dur": 20, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691060, "dur": 19, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691081, "dur": 18, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691101, "dur": 22, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691125, "dur": 19, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691145, "dur": 22, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691169, "dur": 21, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691191, "dur": 20, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691213, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691235, "dur": 20, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691256, "dur": 18, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691278, "dur": 22, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691302, "dur": 18, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691321, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691344, "dur": 17, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691363, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691384, "dur": 18, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691404, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691456, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691481, "dur": 20, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691503, "dur": 18, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691522, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691546, "dur": 18, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691566, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691592, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691593, "dur": 35, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691630, "dur": 26, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691658, "dur": 28, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691688, "dur": 29, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691718, "dur": 24, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691745, "dur": 25, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691772, "dur": 19, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691794, "dur": 31, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691827, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691829, "dur": 26, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691857, "dur": 27, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691888, "dur": 23, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691914, "dur": 20, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691935, "dur": 21, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691960, "dur": 29, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722691992, "dur": 18, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692012, "dur": 27, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692041, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692062, "dur": 19, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692083, "dur": 21, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692107, "dur": 25, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692134, "dur": 21, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692157, "dur": 20, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692178, "dur": 19, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692199, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692200, "dur": 27, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692230, "dur": 27, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692259, "dur": 27, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692289, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692290, "dur": 37, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692330, "dur": 22, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692353, "dur": 24, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692379, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692381, "dur": 25, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692408, "dur": 19, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692429, "dur": 18, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692449, "dur": 26, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692478, "dur": 25, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692505, "dur": 20, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692528, "dur": 120, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692651, "dur": 39, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692693, "dur": 1, "ph": "X", "name": "ProcessMessages 1157", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692695, "dur": 46, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692744, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692745, "dur": 37, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692785, "dur": 30, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692819, "dur": 26, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692847, "dur": 28, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692877, "dur": 26, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692907, "dur": 29, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692938, "dur": 25, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692965, "dur": 22, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722692990, "dur": 29, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693022, "dur": 23, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693048, "dur": 118, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693168, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693192, "dur": 69, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693266, "dur": 34, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693302, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693304, "dur": 29, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693335, "dur": 23, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693360, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693381, "dur": 21, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693405, "dur": 23, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693431, "dur": 33, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693468, "dur": 180, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693652, "dur": 25, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693679, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693702, "dur": 19, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693723, "dur": 21, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693747, "dur": 24, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693772, "dur": 24, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693798, "dur": 18, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693818, "dur": 24, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693845, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693864, "dur": 29, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693896, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693898, "dur": 34, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693934, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693936, "dur": 27, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693965, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693967, "dur": 29, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722693999, "dur": 25, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694025, "dur": 27, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694054, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694055, "dur": 26, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694084, "dur": 18, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694104, "dur": 19, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694124, "dur": 22, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694150, "dur": 33, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694185, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694187, "dur": 32, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694221, "dur": 22, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694245, "dur": 22, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694268, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694270, "dur": 36, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694308, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694309, "dur": 34, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694346, "dur": 32, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694380, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694382, "dur": 40, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694425, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694426, "dur": 31, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694459, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694461, "dur": 24, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694486, "dur": 24, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694513, "dur": 29, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694545, "dur": 30, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694577, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694578, "dur": 30, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694611, "dur": 28, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694642, "dur": 27, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694671, "dur": 22, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694697, "dur": 37, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694736, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694738, "dur": 33, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694775, "dur": 25, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694802, "dur": 20, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694824, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694849, "dur": 22, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694873, "dur": 19, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694894, "dur": 25, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694922, "dur": 33, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694957, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694958, "dur": 20, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722694980, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695006, "dur": 19, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695028, "dur": 23, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695054, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695076, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695078, "dur": 18, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695098, "dur": 21, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695121, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695141, "dur": 26, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695170, "dur": 25, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695199, "dur": 31, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695231, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695256, "dur": 22, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695280, "dur": 22, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695304, "dur": 19, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695325, "dur": 18, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695345, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695367, "dur": 23, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695392, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695412, "dur": 18, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695432, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695451, "dur": 23, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695476, "dur": 19, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695496, "dur": 20, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695518, "dur": 19, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695538, "dur": 2, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695541, "dur": 17, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695559, "dur": 19, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695579, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695601, "dur": 24, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695626, "dur": 19, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695647, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695667, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695689, "dur": 16, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695707, "dur": 21, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695730, "dur": 20, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695752, "dur": 20, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695773, "dur": 20, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695795, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695817, "dur": 18, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695837, "dur": 19, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695857, "dur": 24, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695883, "dur": 42, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695928, "dur": 24, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695954, "dur": 22, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695979, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722695980, "dur": 32, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696016, "dur": 37, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696056, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696058, "dur": 38, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696099, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696100, "dur": 23, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696126, "dur": 24, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696151, "dur": 24, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696179, "dur": 25, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696206, "dur": 45, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696255, "dur": 35, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696292, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696294, "dur": 29, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696325, "dur": 39, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696368, "dur": 41, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696411, "dur": 22, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696435, "dur": 21, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696458, "dur": 27, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696488, "dur": 27, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696518, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696519, "dur": 28, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696549, "dur": 21, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696572, "dur": 20, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696594, "dur": 19, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696615, "dur": 46, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696665, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696666, "dur": 37, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696706, "dur": 22, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696730, "dur": 76, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696810, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696843, "dur": 26, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696871, "dur": 23, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696898, "dur": 19, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696918, "dur": 26, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696947, "dur": 48, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722696996, "dur": 39, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697038, "dur": 1, "ph": "X", "name": "ProcessMessages 941", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697041, "dur": 38, "ph": "X", "name": "ReadAsync 941", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697081, "dur": 27, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697111, "dur": 25, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697139, "dur": 31, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697173, "dur": 1, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697174, "dur": 31, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697207, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697208, "dur": 24, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697235, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697236, "dur": 28, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697267, "dur": 27, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697297, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697299, "dur": 38, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697339, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697341, "dur": 34, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697377, "dur": 32, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697412, "dur": 59, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697475, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697504, "dur": 27, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697534, "dur": 21, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697556, "dur": 29, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697588, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697590, "dur": 27, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697620, "dur": 47, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697669, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697670, "dur": 29, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697701, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697704, "dur": 26, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697732, "dur": 27, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697762, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697764, "dur": 37, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697804, "dur": 29, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697836, "dur": 21, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697861, "dur": 25, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697888, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697924, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697925, "dur": 25, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697953, "dur": 24, "ph": "X", "name": "ReadAsync 15", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722697980, "dur": 24, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722698006, "dur": 54, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722698063, "dur": 33, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722698099, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722698100, "dur": 33, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722698137, "dur": 22, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722698161, "dur": 783, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722698948, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699019, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699047, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699049, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699079, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699080, "dur": 151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699235, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699274, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699373, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699404, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699406, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699435, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699469, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699496, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699527, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699558, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699642, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699683, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699744, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699773, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699775, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699822, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699854, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699856, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699896, "dur": 31, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699930, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722699971, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700004, "dur": 152, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700160, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700199, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700227, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700228, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700267, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700297, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700355, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700390, "dur": 126, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700520, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700559, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700661, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700687, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700748, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700784, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700809, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700918, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700945, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722700947, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701096, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701136, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701138, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701173, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701250, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701283, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701307, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701365, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701387, "dur": 192, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701583, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701624, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701625, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701662, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701664, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701696, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701725, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701809, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701846, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701847, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701882, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701912, "dur": 21, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701935, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722701976, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702007, "dur": 173, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702183, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702214, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702248, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702339, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702367, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702368, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702406, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702448, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702450, "dur": 253, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702707, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702747, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702749, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702783, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702816, "dur": 69, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702889, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702920, "dur": 52, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722702976, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703004, "dur": 110, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703117, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703172, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703173, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703210, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703293, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703328, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703329, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703378, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703407, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703473, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703541, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703575, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703577, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703618, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703651, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703681, "dur": 203, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703888, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703909, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703935, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722703961, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704042, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704073, "dur": 74, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704154, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704191, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704221, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704222, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704252, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704254, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704292, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704347, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704348, "dur": 110, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704463, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704489, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704514, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704537, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704562, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704600, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704636, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704672, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704708, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704747, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704749, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704782, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704849, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704880, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704959, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722704991, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705018, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705054, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705084, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705131, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705154, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705215, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705245, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705319, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705354, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705387, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705496, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705527, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705528, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705551, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705623, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705644, "dur": 160, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705808, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705843, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705845, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705878, "dur": 96, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722705978, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706014, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706083, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706116, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706149, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706182, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706301, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706339, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706415, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706456, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706486, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706517, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706557, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706598, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706627, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706661, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706697, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706724, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706754, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706899, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706935, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722706959, "dur": 3634, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722710596, "dur": 6, "ph": "X", "name": "ProcessMessages 1232", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722710603, "dur": 292, "ph": "X", "name": "ReadAsync 1232", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722710900, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722710925, "dur": 539, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722711467, "dur": 1093, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722712564, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722712597, "dur": 4043, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722716642, "dur": 19562, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722736209, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722736240, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722736242, "dur": 159, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722736405, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722736436, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722736514, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722736546, "dur": 5582, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722742133, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722742165, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722742228, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722742261, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722742290, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722742291, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722742377, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722742401, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722742495, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722742523, "dur": 5566, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722748093, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722748127, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722748210, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722748240, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722748242, "dur": 134, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722748380, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722748407, "dur": 341, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722748749, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722748780, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722748868, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722748900, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722748930, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722748958, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722749003, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722749028, "dur": 439, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722749470, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722749500, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722749531, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722749638, "dur": 138, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722749779, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722749780, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722749806, "dur": 134, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722749945, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722749970, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722750182, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722750211, "dur": 264, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722750480, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722750513, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722750515, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722750593, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722750636, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722750667, "dur": 591, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751264, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751297, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751298, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751367, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751403, "dur": 115, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751522, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751561, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751607, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751639, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751674, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751676, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751702, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751731, "dur": 29, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751765, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751793, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751796, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751825, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751827, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751856, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751887, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751915, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751943, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722751980, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752020, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752059, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752061, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752106, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752136, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752137, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752166, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752212, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752243, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752277, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752279, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752307, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752331, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752333, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752360, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752379, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752407, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752441, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752472, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752503, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752529, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752564, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722752593, "dur": 445, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753041, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753071, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753183, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753211, "dur": 137, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753351, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753388, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753427, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753458, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753489, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753524, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753551, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753579, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753580, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753647, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753649, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753675, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753706, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753733, "dur": 81, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753817, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753845, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753916, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753947, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753973, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722753999, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722754000, "dur": 775, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722754780, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722754817, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722754898, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722754930, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722754961, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755007, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755031, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755066, "dur": 91, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755160, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755193, "dur": 345, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755540, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755543, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755583, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755719, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755754, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755755, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755803, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755836, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755923, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755955, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722755986, "dur": 39, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722756027, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722756074, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722756104, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722756128, "dur": 459, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722756592, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722756623, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722756694, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722756726, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722756751, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722756753, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722756834, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722756860, "dur": 56, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722756920, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722756944, "dur": 320, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722757267, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722757299, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722757378, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722757406, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722757439, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722757467, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722757552, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722757582, "dur": 571, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722758157, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722758187, "dur": 954, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722759145, "dur": 156855, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722916004, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722916006, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722916036, "dur": 12, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722916049, "dur": 16137, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722932189, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722932191, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722932232, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722932235, "dur": 55, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722932292, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722932328, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722932330, "dur": 2623, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722934958, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722934990, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722935003, "dur": 607, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722935614, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722935649, "dur": 9, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722935659, "dur": 433, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722936095, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722936125, "dur": 20, "ph": "X", "name": "ReadAsync 3", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722936146, "dur": 129, "ph": "X", "name": "ProcessMessages 10", "args": {}}, {"pid": 17020, "tid": 21474836480, "ts": 1754045722936276, "dur": 6077, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 17020, "tid": 504, "ts": 1754045722950302, "dur": 1004, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 17020, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 17020, "tid": 17179869184, "ts": 1754045722637327, "dur": 3, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 17020, "tid": 17179869184, "ts": 1754045722637331, "dur": 21030, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 17020, "tid": 17179869184, "ts": 1754045722658361, "dur": 39, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 17020, "tid": 504, "ts": 1754045722951307, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 17020, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 17020, "tid": 1, "ts": 1754045722245975, "dur": 3356, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17020, "tid": 1, "ts": 1754045722249333, "dur": 33933, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17020, "tid": 1, "ts": 1754045722283271, "dur": 30915, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 17020, "tid": 504, "ts": 1754045722951312, "dur": 3, "ph": "X", "name": "", "args": {}}, {"pid": 17020, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722244726, "dur": 15492, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722260219, "dur": 68884, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722260843, "dur": 1823, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722262669, "dur": 978, "ph": "X", "name": "ProcessMessages 5883", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722263651, "dur": 122, "ph": "X", "name": "ReadAsync 5883", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722263775, "dur": 8, "ph": "X", "name": "ProcessMessages 18753", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722263784, "dur": 26, "ph": "X", "name": "ReadAsync 18753", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722263813, "dur": 28, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722263843, "dur": 28, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722263875, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722263877, "dur": 37, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722263916, "dur": 25, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722263943, "dur": 1, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722263945, "dur": 39, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722263986, "dur": 30, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264019, "dur": 21, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264042, "dur": 54, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264099, "dur": 37, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264140, "dur": 29, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264170, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264172, "dur": 33, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264207, "dur": 24, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264234, "dur": 33, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264270, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264272, "dur": 35, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264309, "dur": 26, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264337, "dur": 22, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264361, "dur": 52, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264414, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264416, "dur": 33, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264451, "dur": 24, "ph": "X", "name": "ReadAsync 1087", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264477, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264479, "dur": 33, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264516, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264518, "dur": 34, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264554, "dur": 23, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264579, "dur": 21, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264601, "dur": 23, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264627, "dur": 31, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264660, "dur": 22, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264685, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264707, "dur": 20, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264729, "dur": 20, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264751, "dur": 22, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264775, "dur": 20, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264797, "dur": 27, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264826, "dur": 17, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264845, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264868, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264890, "dur": 20, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264911, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264934, "dur": 24, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264960, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722264982, "dur": 20, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265005, "dur": 20, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265027, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265049, "dur": 19, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265070, "dur": 20, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265092, "dur": 37, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265131, "dur": 20, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265152, "dur": 17, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265172, "dur": 62, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265236, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265255, "dur": 20, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265277, "dur": 20, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265299, "dur": 22, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265323, "dur": 20, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265345, "dur": 19, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265366, "dur": 19, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265387, "dur": 21, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265409, "dur": 17, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265428, "dur": 18, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265448, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265470, "dur": 20, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265492, "dur": 19, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265513, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265534, "dur": 19, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265554, "dur": 18, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265575, "dur": 20, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265596, "dur": 24, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265622, "dur": 21, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265644, "dur": 20, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265666, "dur": 20, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265688, "dur": 20, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265709, "dur": 19, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265730, "dur": 21, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265752, "dur": 17, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265772, "dur": 20, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265793, "dur": 19, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265813, "dur": 20, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265835, "dur": 20, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265857, "dur": 44, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265904, "dur": 37, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265944, "dur": 31, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722265978, "dur": 29, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266009, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266011, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266035, "dur": 28, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266065, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266088, "dur": 22, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266112, "dur": 20, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266133, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266155, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266177, "dur": 20, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266198, "dur": 19, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266219, "dur": 19, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266240, "dur": 17, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266259, "dur": 21, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266281, "dur": 19, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266302, "dur": 18, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266321, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266323, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266344, "dur": 20, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266366, "dur": 20, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266387, "dur": 26, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266414, "dur": 19, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266436, "dur": 18, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266455, "dur": 20, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266476, "dur": 37, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266515, "dur": 23, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266539, "dur": 20, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266561, "dur": 19, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266582, "dur": 19, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266602, "dur": 21, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266627, "dur": 19, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266647, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266648, "dur": 19, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266669, "dur": 20, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266691, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266714, "dur": 20, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266735, "dur": 32, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266769, "dur": 20, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266791, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266813, "dur": 19, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266834, "dur": 19, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266855, "dur": 21, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266877, "dur": 21, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266900, "dur": 20, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266922, "dur": 19, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266943, "dur": 18, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266962, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266964, "dur": 21, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722266988, "dur": 20, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267010, "dur": 21, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267032, "dur": 18, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267052, "dur": 22, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267077, "dur": 21, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267101, "dur": 20, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267124, "dur": 19, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267145, "dur": 21, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267167, "dur": 20, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267189, "dur": 20, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267213, "dur": 21, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267236, "dur": 22, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267261, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267285, "dur": 20, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267306, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267329, "dur": 21, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267352, "dur": 20, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267374, "dur": 20, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267397, "dur": 18, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267417, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267439, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267462, "dur": 172, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267638, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267690, "dur": 1, "ph": "X", "name": "ProcessMessages 1230", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267692, "dur": 32, "ph": "X", "name": "ReadAsync 1230", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267726, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267728, "dur": 37, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267768, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267770, "dur": 21, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267792, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267822, "dur": 27, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267851, "dur": 22, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267875, "dur": 23, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267901, "dur": 25, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267928, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267929, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267954, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267976, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722267998, "dur": 19, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722268019, "dur": 20, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722268041, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722268063, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722268092, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722268094, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722268127, "dur": 30, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722268162, "dur": 25, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722268188, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722268190, "dur": 3549, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722271741, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722271744, "dur": 179, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722271925, "dur": 11, "ph": "X", "name": "ProcessMessages 20556", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722271937, "dur": 35, "ph": "X", "name": "ReadAsync 20556", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722271974, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722271976, "dur": 32, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272011, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272012, "dur": 32, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272047, "dur": 1, "ph": "X", "name": "ProcessMessages 102", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272048, "dur": 45, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272096, "dur": 38, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272137, "dur": 36, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272175, "dur": 30, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272209, "dur": 29, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272240, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272243, "dur": 30, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272276, "dur": 25, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272303, "dur": 28, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272333, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272335, "dur": 33, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272372, "dur": 26, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272400, "dur": 27, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272429, "dur": 25, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272457, "dur": 26, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272486, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272515, "dur": 35, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272553, "dur": 26, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272583, "dur": 31, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272617, "dur": 28, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272646, "dur": 27, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272676, "dur": 17, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272696, "dur": 25, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272724, "dur": 30, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272755, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272758, "dur": 24, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272784, "dur": 45, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272834, "dur": 42, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272878, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272880, "dur": 28, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272909, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272910, "dur": 24, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272936, "dur": 27, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272966, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722272968, "dur": 36, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273007, "dur": 26, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273035, "dur": 30, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273069, "dur": 23, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273094, "dur": 23, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273119, "dur": 22, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273145, "dur": 22, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273170, "dur": 23, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273196, "dur": 22, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273220, "dur": 11, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273233, "dur": 20, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273255, "dur": 22, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273280, "dur": 23, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273305, "dur": 21, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273328, "dur": 23, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273352, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273354, "dur": 23, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273379, "dur": 19, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273401, "dur": 24, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273425, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273427, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273452, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273453, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273477, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273500, "dur": 16, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273518, "dur": 23, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273544, "dur": 30, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273576, "dur": 24, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273602, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273627, "dur": 24, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273652, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273654, "dur": 24, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273680, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273704, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273706, "dur": 23, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273730, "dur": 23, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273755, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273756, "dur": 24, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273783, "dur": 23, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273808, "dur": 19, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273830, "dur": 23, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273855, "dur": 26, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273883, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273887, "dur": 28, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273918, "dur": 21, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273941, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273965, "dur": 21, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273987, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722273989, "dur": 21, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274012, "dur": 16, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274029, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274052, "dur": 21, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274075, "dur": 21, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274098, "dur": 21, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274120, "dur": 21, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274143, "dur": 84, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274229, "dur": 32, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274263, "dur": 1, "ph": "X", "name": "ProcessMessages 1302", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274265, "dur": 20, "ph": "X", "name": "ReadAsync 1302", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274287, "dur": 23, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274312, "dur": 21, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274335, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274356, "dur": 18, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274377, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274400, "dur": 21, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274422, "dur": 21, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274445, "dur": 17, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274464, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274487, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274509, "dur": 20, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274533, "dur": 20, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274554, "dur": 21, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274577, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274600, "dur": 26, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274628, "dur": 20, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722274651, "dur": 436, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275090, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275119, "dur": 144, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275265, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275304, "dur": 175, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275482, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275484, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275517, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275559, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275562, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275615, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275642, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275854, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275891, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275927, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275959, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722275961, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276052, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276093, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276097, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276136, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276179, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276212, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276250, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276252, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276295, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276328, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276330, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276372, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276433, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276467, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276495, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276497, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276526, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276592, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276624, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276627, "dur": 263, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276893, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276895, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722276930, "dur": 154, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277087, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277124, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277127, "dur": 173, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277304, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277338, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277468, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277496, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277497, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277527, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277582, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277633, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277636, "dur": 61, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277700, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277701, "dur": 199, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277905, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722277958, "dur": 61, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722278023, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722278050, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722278079, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722278102, "dur": 2961, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281068, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281125, "dur": 6, "ph": "X", "name": "ProcessMessages 1268", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281133, "dur": 32, "ph": "X", "name": "ReadAsync 1268", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281169, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281202, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281204, "dur": 155, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281363, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281387, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281484, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281512, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281590, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281617, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281650, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281673, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281704, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281733, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281762, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281791, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281819, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281843, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281872, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281896, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722281990, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282015, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282039, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282111, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282143, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282186, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282218, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282220, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282301, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282341, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282343, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282384, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282418, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282444, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282467, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282523, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282546, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282604, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282633, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282739, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282764, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282850, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282853, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282886, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282887, "dur": 28, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282919, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282947, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722282988, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283017, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283045, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283072, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283140, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283166, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283309, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283340, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283387, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283416, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283443, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283475, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283508, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283549, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283585, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283623, "dur": 21, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283647, "dur": 233, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283884, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283913, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722283938, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284005, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284033, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284035, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284072, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284097, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284124, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284158, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284189, "dur": 210, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284404, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284437, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284466, "dur": 130, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284601, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284637, "dur": 51, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284691, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284729, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284755, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284794, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284825, "dur": 157, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722284985, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285011, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285042, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285076, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285226, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285262, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285264, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285297, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285335, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285366, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285393, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285416, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285525, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285556, "dur": 100, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285661, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285688, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285715, "dur": 171, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285890, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285977, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722285978, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722286007, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722286009, "dur": 23, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722286036, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722286077, "dur": 24346, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722310428, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722310431, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722310464, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722310595, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722310626, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722310656, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722310682, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722310798, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722310841, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722310843, "dur": 6175, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722317022, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722317058, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722317129, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722317161, "dur": 255, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722317420, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722317457, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722317537, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722317562, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722317677, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722317749, "dur": 5063, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722322815, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722322846, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722323004, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722323032, "dur": 172, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 17020, "tid": 12884901888, "ts": 1754045722323206, "dur": 5527, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 17020, "tid": 504, "ts": 1754045722951316, "dur": 398, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 17020, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 17020, "tid": 8589934592, "ts": 1754045722243037, "dur": 71176, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 17020, "tid": 8589934592, "ts": 1754045722314215, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 17020, "tid": 8589934592, "ts": 1754045722314220, "dur": 1227, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 17020, "tid": 504, "ts": 1754045722951715, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 17020, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 17020, "tid": 4294967296, "ts": 1754045722226985, "dur": 102718, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 17020, "tid": 4294967296, "ts": 1754045722230286, "dur": 8770, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 17020, "tid": 4294967296, "ts": 1754045722329783, "dur": 301168, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 17020, "tid": 4294967296, "ts": 1754045722631081, "dur": 311299, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 17020, "tid": 4294967296, "ts": 1754045722631164, "dur": 5814, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 17020, "tid": 4294967296, "ts": 1754045722942562, "dur": 2440, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 17020, "tid": 4294967296, "ts": 1754045722944167, "dur": 25, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 17020, "tid": 4294967296, "ts": 1754045722945005, "dur": 6, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 17020, "tid": 504, "ts": 1754045722951719, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754045722659223, "dur": 28100, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754045722687326, "dur": 110, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754045722687498, "dur": 368, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754045722688789, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1CFBC647265B7D61.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754045722689230, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_42F8665F9413CA2D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754045722690407, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_B9E05D4615C858EB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754045722691071, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_19B07DA70A32A185.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754045722694028, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754045722694165, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754045722694318, "dur": 189, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754045722697046, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754045722698160, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754045722698230, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754045722698747, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754045722687882, "dur": 11100, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754045722698990, "dur": 237531, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754045722936522, "dur": 223, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754045722936938, "dur": 1585, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754045722688147, "dur": 10880, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722699032, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_184A04796BB6AF30.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722699530, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722699774, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_A3598586C48DAB89.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722700011, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722700479, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_E9025F1EBE6B906B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722700840, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722701110, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_E742B87826F4607B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722701567, "dur": 578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722702150, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_86942DFBA173BD2F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722702418, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722702693, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_61B9003D7A1B656E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722703174, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722703636, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_A982C66D2D348ED7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722703970, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722704263, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9582B39E166EA023.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722704695, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722705083, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722705339, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722705713, "dur": 746, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722706487, "dur": 756, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722707249, "dur": 532, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722707808, "dur": 583, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722708396, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722708975, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722709513, "dur": 693, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722710211, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722713849, "dur": 741, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Utilities\\ObjectReferenceField.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754045722714590, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Utilities\\ObjectExtension.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754045722710693, "dur": 5410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722716104, "dur": 3090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722719195, "dur": 2677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722721872, "dur": 3072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722724945, "dur": 2629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722727574, "dur": 2528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722730103, "dur": 2796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722733916, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Runtime\\Evaluation\\ScheduleRuntimeClip.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754045722732900, "dur": 2917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722735818, "dur": 2282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722738100, "dur": 3192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722741292, "dur": 2314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722743606, "dur": 4612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722748218, "dur": 751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722748969, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722749641, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722749775, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722749899, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722750308, "dur": 864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754045722751172, "dur": 1436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722752620, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722752774, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722752891, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722753056, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754045722753454, "dur": 731, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722754197, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722754331, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722754415, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722754822, "dur": 834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722755656, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722755783, "dur": 670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722756453, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722756691, "dur": 768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722757459, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722757549, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722757682, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722758143, "dur": 178376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722688151, "dur": 10885, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722699042, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C9FC54791F8F8DD6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722699649, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722699875, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_816918F4C6F4A52A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722700119, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722700608, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_8BE78E10F7CFA16B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722701105, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722701503, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_B337F2B9F8637F8B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722701636, "dur": 757, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722702401, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_C6B01611FE9BE340.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722702595, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722702766, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_4BCB4A18FFEC0881.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722703196, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722703628, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_8C95FB58FA13BAB6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722703953, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722704224, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E6F1BAA280F85427.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722704665, "dur": 462, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722705135, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722705336, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754045722705392, "dur": 490, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722705894, "dur": 788, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722706688, "dur": 673, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722707375, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722707929, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722708537, "dur": 496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722709042, "dur": 515, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722709578, "dur": 612, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722710190, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754045722712447, "dur": 976, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754045722715334, "dur": 614, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\AnimationTrackInspector.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754045722713425, "dur": 3571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722716996, "dur": 3016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722720012, "dur": 2695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722722708, "dur": 3051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722728248, "dur": 527, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Meta\\DictionaryValueAtIndexMetadata.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754045722725759, "dur": 3302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722729061, "dur": 2645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722731706, "dur": 3042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722734748, "dur": 2901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722737649, "dur": 2470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722740120, "dur": 2340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722742461, "dur": 2821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722745282, "dur": 3675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722748957, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722749626, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722749750, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722749899, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722750343, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754045722750668, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722751290, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722751466, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722751544, "dur": 540, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722752091, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754045722752340, "dur": 596, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722752949, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722753103, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722753316, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722753911, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722754270, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722754422, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722754525, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722754626, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722754697, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722754826, "dur": 696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754045722755522, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722755769, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722755850, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722755915, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722755993, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754045722756392, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722756685, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722756762, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722756838, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722756918, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754045722757166, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722757458, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722757594, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722757682, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722757763, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754045722757944, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722758155, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722758224, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722758295, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722758657, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754045722758393, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754045722760100, "dur": 156735, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754045722932896, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754045722932840, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754045722933080, "dur": 3384, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754045722688091, "dur": 10916, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722699016, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_FC130522DD32B68F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722699498, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722699657, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_610A92F577D74C23.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722700055, "dur": 513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722700572, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CB0C3B3019515683.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722701112, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722701356, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_F538D3A612872E43.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722701558, "dur": 558, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722702123, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_2B292166361825BD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722702457, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722702750, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_2E0DBB4CDB1259B3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722703189, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722703547, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_4A13D76A06B15607.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722703688, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722704006, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_13697A979EB0EF82.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722704435, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722704779, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722704988, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722705127, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722705415, "dur": 631, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722706052, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754045722706112, "dur": 699, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722706818, "dur": 617, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722707449, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722707978, "dur": 608, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722708591, "dur": 541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722709167, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722709713, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722710269, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722710758, "dur": 3541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722714299, "dur": 3125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722717425, "dur": 2936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722720361, "dur": 2916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722723277, "dur": 2860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722726138, "dur": 2849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722730851, "dur": 545, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticFunctionInvoker_5.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754045722728987, "dur": 3642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722732629, "dur": 2025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722734655, "dur": 2690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722737345, "dur": 3201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722740547, "dur": 2459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722743009, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722743117, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722743215, "dur": 2479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722745694, "dur": 3268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722748963, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722749626, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722749780, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722749920, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722750430, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754045722750846, "dur": 1244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722752108, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722752207, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722752373, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722752444, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722752792, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722753162, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722753346, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722753898, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722754029, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722754284, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722754561, "dur": 1079, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722755643, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722755777, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722756408, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722756557, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722756675, "dur": 773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722757451, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722757582, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722758159, "dur": 178391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722688325, "dur": 10740, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722699071, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_F279AD6DB3287D52.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722699853, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722700207, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1CFBC647265B7D61.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722700373, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722700721, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_17A99C1F49AC544C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722701180, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722701606, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_1F33D875853821BA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722701817, "dur": 645, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722702467, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_F4C63E5BAEA27403.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722702856, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722703065, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_B9E05D4615C858EB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722703344, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722703736, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_20FB01B34163AD70.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722704111, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722704390, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_CB3E21EA52206828.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722704780, "dur": 464, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722705260, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722705412, "dur": 550, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722705976, "dur": 711, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722706696, "dur": 666, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722707377, "dur": 591, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722707973, "dur": 561, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722708538, "dur": 515, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722709063, "dur": 514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722709595, "dur": 593, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722710188, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754045722710262, "dur": 474, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722710739, "dur": 4063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722714803, "dur": 4085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722719096, "dur": 714, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Flow\\Framework\\Control\\SwitchOnEnumDescriptor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754045722718889, "dur": 3547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722722436, "dur": 3208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722725645, "dur": 2733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722728378, "dur": 3171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722731549, "dur": 2508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722734058, "dur": 2682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722736740, "dur": 2624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722739364, "dur": 2908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722742272, "dur": 2625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722744897, "dur": 3343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722748241, "dur": 720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722748961, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722749627, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722749753, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722749944, "dur": 521, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722750469, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754045722750883, "dur": 1552, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722752443, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722752576, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722752867, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722752966, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722753059, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754045722753437, "dur": 813, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722754271, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722754363, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722754421, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722754818, "dur": 835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722755654, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722755766, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722756424, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722756674, "dur": 778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722757453, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722757548, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722758141, "dur": 178379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722688129, "dur": 10888, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722699023, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_7A4B826FC3B9A562.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722699555, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722699831, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_B1046D03C7B936D0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722700065, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722700525, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_8B4EA0E95E9C463B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722701078, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722701191, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_8E5076AB386A4F5F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722701564, "dur": 634, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722702204, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_EBEA4291B5996ADA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722702425, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722702731, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E3304A047EE08E0B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722703181, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722703540, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_574D2D579249D9A8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722703793, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722703968, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_574D2D579249D9A8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722704021, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_23DE2903C5ADDDF2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722704431, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722704788, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722705037, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722705372, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722705692, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722705815, "dur": 862, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722706682, "dur": 29907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754045722736590, "dur": 437, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722737067, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722737376, "dur": 2169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722739546, "dur": 2563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722742110, "dur": 2812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722744923, "dur": 1739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722746662, "dur": 2302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722748964, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722749643, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722749853, "dur": 1434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722751290, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722751436, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722752110, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722752232, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722752374, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722752614, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722752978, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722753281, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722753406, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754045722753594, "dur": 796, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722754401, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722754749, "dur": 902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722755652, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722755766, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722755888, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722756414, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722756676, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722757467, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722757558, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722758140, "dur": 174709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722932976, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1754045722932850, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1754045722933128, "dur": 2664, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1754045722935795, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722688082, "dur": 10916, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722705045, "dur": 593, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/UnityEditor/6000.1.14f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1754045722699005, "dur": 6633, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722705677, "dur": 696, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722706385, "dur": 752, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722707147, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722707754, "dur": 607, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722708365, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722708894, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722709383, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722709960, "dur": 527, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722710492, "dur": 3881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722714374, "dur": 3144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722717518, "dur": 2641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722720159, "dur": 2735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722722895, "dur": 2547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722725442, "dur": 2877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722728319, "dur": 2491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722730810, "dur": 2630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722733440, "dur": 3068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722736509, "dur": 3099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722739609, "dur": 3018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722742628, "dur": 2913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722745541, "dur": 3428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722748969, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722749646, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722749811, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754045722750051, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722750543, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754045722750940, "dur": 1545, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722752494, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722752733, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754045722752882, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722753104, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754045722753659, "dur": 754, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722754422, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722754554, "dur": 1088, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722755645, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722755781, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722756430, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722756672, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722756769, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754045722756859, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722756960, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754045722757196, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722757565, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722757676, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722758157, "dur": 178389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722688243, "dur": 10803, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722699051, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_5DC9A836C807FAF2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722699741, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722699900, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_C048E937E9A421B8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722700125, "dur": 543, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722700674, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_339B7DA446DBACC5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722701183, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722701612, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_CAEDB152A2C80D92.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722701897, "dur": 580, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722702482, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_F184BD0C8FDF7284.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722702911, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722703073, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_968B718F611B56F9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722703319, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722703740, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_386811273654939A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722704054, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722704379, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_27CD92644E7249A2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722704715, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722705155, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_A45CE29EBBBE7D91.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722705333, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722705611, "dur": 721, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722706343, "dur": 621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722706968, "dur": 550, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722707546, "dur": 494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722708044, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722708598, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722709162, "dur": 491, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722709671, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722710240, "dur": 496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722710744, "dur": 3669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722714414, "dur": 2562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722716977, "dur": 3070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722722440, "dur": 664, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Time\\WaitForFlow.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754045722720048, "dur": 3057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722723105, "dur": 2694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722725799, "dur": 2526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722728325, "dur": 2541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722730866, "dur": 2616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722733482, "dur": 2642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722736124, "dur": 2418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722738542, "dur": 2215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722740758, "dur": 2642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722743401, "dur": 2217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722745618, "dur": 3353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722748971, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722749643, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722749826, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722749969, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722750513, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754045722750931, "dur": 1491, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722752437, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722752556, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722752805, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722753185, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722753914, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722754269, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722754435, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722754526, "dur": 1117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722755643, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722755776, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722756406, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722756561, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722756686, "dur": 765, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722757451, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722757551, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722758141, "dur": 178382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722688280, "dur": 10774, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722699060, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_C9F914A74E2B16B8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722699807, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722699891, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_7575D6D4E1069A15.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722700119, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722700664, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EDFFA85DD2BD1FB2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722701114, "dur": 417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722701536, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_7FF844E13374B2E6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722701643, "dur": 756, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722702405, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_37301B1867509DEE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722702639, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722702817, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_6FFB81672CC7662A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722703212, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722703638, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_3D9F6B68BD00EB1C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722703958, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722704222, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_94172ADB7F54A7D8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722704590, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722705046, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722705173, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722705343, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722705801, "dur": 837, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722706667, "dur": 577, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722707253, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722707807, "dur": 551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722708363, "dur": 561, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722708934, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722709438, "dur": 577, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722710024, "dur": 470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722710499, "dur": 3133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722713632, "dur": 2876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722716508, "dur": 2554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722719379, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Flow\\Description\\UnitPortDescription.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754045722719063, "dur": 3154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722722218, "dur": 2960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722725178, "dur": 2573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722727751, "dur": 2668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722730419, "dur": 2730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722733150, "dur": 3578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722736728, "dur": 3012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722739740, "dur": 2730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722742470, "dur": 3057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722745528, "dur": 3427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722748958, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722749073, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722749635, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722749809, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722750056, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722750548, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754045722750915, "dur": 1441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722752374, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722752487, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722752782, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722753098, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722753251, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722753923, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722754271, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722754416, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722754813, "dur": 844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722755658, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722755778, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722756429, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722756677, "dur": 778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722757455, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722757549, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722758144, "dur": 178373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722688365, "dur": 10711, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722699082, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_612DCBB3052D864A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722699866, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722700281, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_B89CB73E89E58DB9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722700682, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722701002, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B40E848BC76ACCC7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722701494, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722701971, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_30E83EFD1EA5B11D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722702071, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722702536, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_0E440929134BF079.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722703036, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722703257, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_1E7AA3722A1C93AD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722703554, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722703958, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_FD894A3BC08E0559.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722704373, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722704731, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722704888, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_AC876CD17C0FF1C1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722705023, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722705392, "dur": 523, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722705954, "dur": 733, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722706695, "dur": 645, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722707352, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722707918, "dur": 566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722708491, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722709008, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722709561, "dur": 621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722710195, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722710687, "dur": 3278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722714307, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Animation\\AnimationOffsetMenu.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754045722713965, "dur": 3291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722717257, "dur": 2989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722720246, "dur": 2624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722722871, "dur": 2735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722725607, "dur": 2967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722728574, "dur": 2830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722731404, "dur": 2455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722733859, "dur": 2399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722736258, "dur": 2688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722738946, "dur": 2709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722741656, "dur": 98, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722741776, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722742307, "dur": 2263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722744571, "dur": 3832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722748403, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722748967, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722749645, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722749759, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722749958, "dur": 815, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722750781, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754045722751076, "dur": 1450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722752533, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722752710, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722752993, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722753129, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722753221, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754045722753561, "dur": 697, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722754278, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722754526, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722754833, "dur": 828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722755662, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722755771, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722756407, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722756673, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722756796, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722757463, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722757575, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722758151, "dur": 178378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722688404, "dur": 10686, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722699099, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_36C5F3CCEBE3CD07.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722699854, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722700212, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_FADE1643CA5C8800.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722700413, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722700728, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_ED2D3EB1C97F3A5F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722701180, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722701632, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AC1B2F844B840E57.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722701888, "dur": 578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722702471, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_14260DC7C6A62D51.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722702855, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722703022, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_00A3E5D2DC969885.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722703297, "dur": 423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722703732, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_941F941CDF3DCEAF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722704033, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722704375, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_19B07DA70A32A185.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722704812, "dur": 498, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722705337, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722705558, "dur": 659, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722706222, "dur": 694, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722706924, "dur": 576, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722707522, "dur": 515, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722708041, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722708640, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722709168, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722709712, "dur": 558, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722710292, "dur": 558, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722710853, "dur": 3153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722714006, "dur": 3722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722717729, "dur": 2778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722720507, "dur": 2626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722723134, "dur": 2946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722726080, "dur": 2963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722729043, "dur": 3172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722732216, "dur": 2888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722735105, "dur": 2493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722737599, "dur": 2374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722739974, "dur": 3290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722743265, "dur": 2284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722745550, "dur": 3409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722748959, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722749628, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722749835, "dur": 1459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722751294, "dur": 818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722752112, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722752374, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722752440, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722752771, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722753154, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722753319, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722753898, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722754032, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722754275, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722754418, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722754810, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722755656, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722755796, "dur": 641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722756437, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722756672, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722756787, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722757453, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722757551, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722758141, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722758284, "dur": 178264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722688440, "dur": 10662, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722699107, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_2202EAFAEF1B6C1F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722699850, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722700073, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_9919525412EAD2E7.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722700181, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722700670, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_42F8665F9413CA2D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722701133, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722701589, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_805911B8F5770868.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722701779, "dur": 661, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722702444, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_7887D396BEE6166C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722702758, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722702829, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_1DAA338F289F96AC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722703191, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722703555, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A2ECA603E2EEF8E4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722703823, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722703970, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A2ECA603E2EEF8E4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722704028, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_23CB3E72B95C6251.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722704458, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722704885, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_7DC540854D1BBB01.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722705000, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722705365, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722705823, "dur": 839, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722706668, "dur": 611, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722707292, "dur": 571, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722707867, "dur": 617, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722708488, "dur": 545, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722709045, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722709583, "dur": 605, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722710216, "dur": 474, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722712318, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Utilities\\AnimatedParameterCache.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754045722710696, "dur": 4045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722714741, "dur": 3308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722718049, "dur": 3507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722721557, "dur": 3109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722724666, "dur": 2417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722727083, "dur": 2659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722729743, "dur": 2792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722732535, "dur": 2955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722735491, "dur": 2611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722738103, "dur": 3025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722741129, "dur": 2308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722743437, "dur": 4338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722747776, "dur": 1187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722748963, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722749625, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722749852, "dur": 1437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722751292, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722751457, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722752108, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722752223, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722752374, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722752688, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722753110, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722753334, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722753897, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722754204, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722754334, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722754421, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722754799, "dur": 855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722755655, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722755765, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722755894, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722756434, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722756683, "dur": 763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722757449, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722757552, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722758146, "dur": 178392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722688523, "dur": 10602, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722699132, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_61250575CD81DD6E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722699872, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722700299, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_825F83770C6FBB2D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722700693, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722701036, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_98ABF5EF82F6C903.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722701393, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722701755, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B16C90EDBDE4DF78.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722701904, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722702492, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_66EAD504DFBBEDF3.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722702935, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722703178, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_885799F0F6E77EBF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722703409, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722703819, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A9C25E502CB87508.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722704132, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722704457, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_3861702E21F7C7AB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722704753, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722705151, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_220A2C8EABC83166.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722705335, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722705577, "dur": 754, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722706337, "dur": 678, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722707019, "dur": 544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722707585, "dur": 527, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722708116, "dur": 548, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722708668, "dur": 503, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722709197, "dur": 559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722709764, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722710316, "dur": 3717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722714033, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@ad30dfd93d36\\Editor\\TMP\\TMPro_EditorShaderUtilities.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754045722715096, "dur": 584, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@ad30dfd93d36\\Editor\\TMP\\PropertyDrawers\\TMP_SpriteCharacterPropertyDrawer.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754045722714033, "dur": 3670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722717703, "dur": 3330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722721034, "dur": 3464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722724498, "dur": 3652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722728150, "dur": 2473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722730623, "dur": 2796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722733420, "dur": 4362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722737782, "dur": 2220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722741910, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\UI\\DialogWithCheckBox.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754045722740002, "dur": 2925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722742927, "dur": 2641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722745568, "dur": 3390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722748958, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722749625, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722749849, "dur": 1442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722751292, "dur": 816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722752108, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722752376, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722752439, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722752779, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722753175, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722753928, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722754271, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722754421, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722754529, "dur": 1116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722755645, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722755773, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722756439, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722756681, "dur": 768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722757450, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722757553, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722758142, "dur": 178403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722688484, "dur": 10629, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722699120, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_D9842110F5874C86.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722699860, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722700216, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_E0A580A67B9BA514.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722700482, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722700807, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DB41EC179EB56DBA.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722701261, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722701643, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FCA09B517E031ABB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722701891, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722702488, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8FAB570D0B1C8DCD.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722703017, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722703265, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_48A7353BAEF13316.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722703559, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722703955, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_86405D0799B6BB11.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722704311, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722704770, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722705063, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722705270, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722705376, "dur": 460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722705843, "dur": 836, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722706684, "dur": 597, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722707294, "dur": 551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722707849, "dur": 566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722708418, "dur": 567, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722708995, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722709531, "dur": 650, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722710186, "dur": 559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722712376, "dur": 539, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Signals\\SignalReceiverHeader.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754045722710750, "dur": 3722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722714472, "dur": 3417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722717889, "dur": 2687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722720576, "dur": 3217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722723794, "dur": 2929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722726724, "dur": 3143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722729867, "dur": 2819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722732686, "dur": 2912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722735599, "dur": 2598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722738197, "dur": 2969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722741166, "dur": 2454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722743620, "dur": 4206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722747826, "dur": 1141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722748967, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722749636, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722749752, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722749942, "dur": 1067, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722751016, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754045722751216, "dur": 1411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722752633, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722752840, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722753007, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722753189, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754045722753596, "dur": 795, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722754421, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722754557, "dur": 1087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722755644, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722755767, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722756441, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722756682, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722757464, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722757571, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722758153, "dur": 178381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722688567, "dur": 10568, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722699140, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_9C0B6EFD81BDE75D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722699879, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722700324, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_CA47B9E89BF3744B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722700698, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722701080, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_054B6FF5C640A1AD.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722701536, "dur": 572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722702114, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_FCA4F4639D8CB7B1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722702260, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722702650, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_90D0F47A19362C10.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722703174, "dur": 453, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722703632, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_092522C3C590FAD2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722703954, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722704132, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_B359FB15FAFC6B04.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722704496, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722704885, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722705069, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722705325, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722705461, "dur": 691, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722706159, "dur": 755, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722706921, "dur": 563, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722707496, "dur": 515, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722708019, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722708595, "dur": 560, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722709165, "dur": 576, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722709745, "dur": 537, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722710285, "dur": 2891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722713176, "dur": 2842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722716018, "dur": 2900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722718919, "dur": 2694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722721613, "dur": 2697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722724310, "dur": 2763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722727074, "dur": 2737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722729811, "dur": 3004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722732815, "dur": 2430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722735246, "dur": 2838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722738084, "dur": 2882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722740966, "dur": 2406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722743372, "dur": 2282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722745654, "dur": 3300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722748957, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722749077, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722749631, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722749754, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722750041, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722750561, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754045722750978, "dur": 1497, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722752482, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722752653, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722752778, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722752955, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754045722753424, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722753916, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722754029, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722754274, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722754525, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722754839, "dur": 807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722755647, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722755765, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722756439, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722756687, "dur": 769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722757456, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722757548, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722757678, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722758156, "dur": 178380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722688608, "dur": 10535, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722699149, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_9E563BCFB42744CB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722699905, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722700367, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_57991EBFA1A26EC2.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722700696, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722701063, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_0A3076E440EF1041.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722701407, "dur": 515, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722701930, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_347DC0FF07300132.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722702038, "dur": 483, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722702526, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_1DFFFA9F10D99920.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722703019, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722703258, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_00BEFECA1F6D79F8.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722703558, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722703962, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_8B112C68541FCD06.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722704392, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722704810, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722705042, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722705375, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722705631, "dur": 705, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722706349, "dur": 794, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722707149, "dur": 587, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722707766, "dur": 563, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722708333, "dur": 560, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722708903, "dur": 473, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722709402, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722709985, "dur": 493, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722710482, "dur": 3518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722714001, "dur": 2985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722716986, "dur": 2410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722719397, "dur": 3171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722722568, "dur": 2857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722725425, "dur": 2390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722727815, "dur": 2576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722730391, "dur": 2983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722733374, "dur": 3673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722737096, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722737258, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722737377, "dur": 2854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722740231, "dur": 2423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722742654, "dur": 2369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722745142, "dur": 3820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722748962, "dur": 676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722749638, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722749796, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722749941, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722750462, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754045722751003, "dur": 1528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722752540, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722752787, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722753182, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722753921, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722754275, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722754541, "dur": 1104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722755645, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722755765, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722756414, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722756673, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722757460, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722757557, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722758140, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722758281, "dur": 178284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722688661, "dur": 10493, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722699155, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_F22C2CAEEE3C250C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722699907, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722700376, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_59BEFA3164F84BCE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722700706, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722701104, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_521EBAF2E01427CC.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722701548, "dur": 526, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722702083, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_6A2198A2D22CCFAF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722702210, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722702647, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_51F15FE6673D9942.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722703150, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722703248, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_AB176FE8EEF474FE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722703551, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722703843, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_9FDB4296FD5402ED.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722704196, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722704493, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_4B5328702BD9392E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722704772, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722705330, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722705446, "dur": 606, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722706058, "dur": 36447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754045722742506, "dur": 458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722742977, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722743073, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722743168, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722743336, "dur": 5378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754045722748715, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722748966, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722749052, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722749138, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722749216, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754045722749427, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722749630, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722749733, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722749878, "dur": 556, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722750438, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754045722751280, "dur": 1334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722752629, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722752865, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722753014, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722753358, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754045722754125, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722754525, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722754620, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722754684, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722754778, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754045722755419, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722755657, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722755738, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722755813, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722755890, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754045722756153, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722756435, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722756553, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722756685, "dur": 774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722757459, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722757573, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722758152, "dur": 178403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754045722941267, "dur": 1606, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1754045722409265, "dur": 204418, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754045722409986, "dur": 53515, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754045722574639, "dur": 2959, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754045722577600, "dur": 36078, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754045722578938, "dur": 27830, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754045722619151, "dur": 994, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754045722618805, "dur": 1486, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754045722257769, "dur": 2538, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754045722260316, "dur": 354, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754045722260744, "dur": 369, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754045722261575, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_610A92F577D74C23.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754045722261866, "dur": 115, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_CA47B9E89BF3744B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754045722262838, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8FAB570D0B1C8DCD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754045722262972, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_0E440929134BF079.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754045722263024, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_51F15FE6673D9942.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754045722263130, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_2E0DBB4CDB1259B3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754045722263518, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_574D2D579249D9A8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754045722269020, "dur": 2376, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754045722261133, "dur": 12950, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754045722274091, "dur": 44699, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754045722318791, "dur": 2333, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754045722321142, "dur": 1159, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754045722322460, "dur": 1734, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754045722261406, "dur": 12738, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722274150, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_5DC9A836C807FAF2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722274745, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722274932, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_C048E937E9A421B8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722275175, "dur": 569, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722275752, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_339B7DA446DBACC5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722276344, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722276547, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AC1B2F844B840E57.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722276938, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722277368, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_7887D396BEE6166C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722277742, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722278029, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_6FFB81672CC7662A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722278387, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722278683, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_3D9F6B68BD00EB1C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722279090, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722279202, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_19B07DA70A32A185.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754045722279841, "dur": 681, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722280539, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722281047, "dur": 591, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722281642, "dur": 550, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722282212, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722282765, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722283339, "dur": 504, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722283875, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722284443, "dur": 661, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722285110, "dur": 3023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722288133, "dur": 2890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722291024, "dur": 2696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722293721, "dur": 2668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722296389, "dur": 2782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722299171, "dur": 2721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722301893, "dur": 2515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722304408, "dur": 2851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722307260, "dur": 1958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722309218, "dur": 2496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722311714, "dur": 2784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722314499, "dur": 2365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722316866, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722316994, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754045722317131, "dur": 3950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722261354, "dur": 12759, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722274120, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_7A4B826FC3B9A562.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722274536, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722274628, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_B1046D03C7B936D0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722275065, "dur": 614, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722275686, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EDFFA85DD2BD1FB2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722276143, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722276339, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_B337F2B9F8637F8B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722276908, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722277358, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_37301B1867509DEE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722277906, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722278114, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_00A3E5D2DC969885.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722278495, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722278795, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_20FB01B34163AD70.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722279191, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722279319, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_3861702E21F7C7AB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754045722279915, "dur": 635, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722280589, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722281143, "dur": 620, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722281767, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722282345, "dur": 563, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722282929, "dur": 541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722283483, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722284075, "dur": 598, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722284686, "dur": 647, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722285340, "dur": 3447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722288787, "dur": 2780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722291568, "dur": 3694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722295263, "dur": 2973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722298236, "dur": 2436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722300673, "dur": 2619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722303292, "dur": 2452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722305744, "dur": 2799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722308544, "dur": 2419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722310963, "dur": 2442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722313406, "dur": 2639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754045722316045, "dur": 2832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722261395, "dur": 12738, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722274141, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C9FC54791F8F8DD6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722274678, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722274747, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_7575D6D4E1069A15.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722275092, "dur": 593, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722275692, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_42F8665F9413CA2D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722276234, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722276346, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_805911B8F5770868.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722276794, "dur": 551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722277354, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_C6B01611FE9BE340.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722277914, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722278134, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_B9E05D4615C858EB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722278530, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722278835, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A9C25E502CB87508.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722279199, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722279326, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_4B5328702BD9392E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722279915, "dur": 680, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722280605, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722280675, "dur": 599, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722281280, "dur": 34879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754045722316160, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722316477, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722316580, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754045722316677, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754045722316867, "dur": 5223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754045722322091, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722261346, "dur": 12754, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722274112, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_FC130522DD32B68F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722274435, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722274543, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_A3598586C48DAB89.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722275064, "dur": 563, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722275635, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CB0C3B3019515683.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722276141, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722276343, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_7FF844E13374B2E6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722276755, "dur": 726, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722277489, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_F184BD0C8FDF7284.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722277889, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722278061, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_1DAA338F289F96AC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722278407, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722278700, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_941F941CDF3DCEAF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722279094, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722279205, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_27CD92644E7249A2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754045722279907, "dur": 633, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722280544, "dur": 552, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722281113, "dur": 645, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722281762, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722282328, "dur": 548, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722282881, "dur": 499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722283389, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722283934, "dur": 572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722284510, "dur": 648, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722288089, "dur": 747, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Window\\TimelineWindow_TrackGui.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754045722285163, "dur": 4110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722289273, "dur": 2902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722292175, "dur": 2877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722295052, "dur": 3131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722298184, "dur": 2444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722300628, "dur": 2469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722304337, "dur": 576, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\OptimizedReflection.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754045722303097, "dur": 3227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722306324, "dur": 2744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722309068, "dur": 2276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722313498, "dur": 506, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\ShelvePendingChangesDialog.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754045722311344, "dur": 2773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722314117, "dur": 2353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754045722316470, "dur": 2339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722261653, "dur": 12559, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722274217, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_D9842110F5874C86.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722274897, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722275307, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_B89CB73E89E58DB9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722275404, "dur": 497, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722275916, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_0A3076E440EF1041.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722276542, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722276985, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_FCA4F4639D8CB7B1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722277385, "dur": 492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722277882, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_90D0F47A19362C10.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722278175, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722278498, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_4A13D76A06B15607.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722278798, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722279100, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_23DE2903C5ADDDF2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722279551, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722279700, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754045722279896, "dur": 627, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722280529, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754045722280585, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722281147, "dur": 617, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722281776, "dur": 550, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722282352, "dur": 575, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722282931, "dur": 541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722283496, "dur": 644, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722284169, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722284765, "dur": 632, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722285401, "dur": 3438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722288839, "dur": 2884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722291723, "dur": 3111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722295935, "dur": 506, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Normalize.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754045722294834, "dur": 3516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722298351, "dur": 2489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722300841, "dur": 2757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722303599, "dur": 2823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722306422, "dur": 2586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722309008, "dur": 2367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722311375, "dur": 2730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722314106, "dur": 2197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722316303, "dur": 2304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754045722318623, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722261385, "dur": 12737, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722274128, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_184A04796BB6AF30.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754045722274601, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722274692, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_816918F4C6F4A52A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754045722275065, "dur": 612, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722275684, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_8BE78E10F7CFA16B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754045722276156, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722276351, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_1F33D875853821BA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754045722276987, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722277548, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_1DFFFA9F10D99920.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754045722278028, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722278327, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_00BEFECA1F6D79F8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754045722278674, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722279029, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_8B112C68541FCD06.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754045722279393, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722279499, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722279648, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754045722279765, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722280188, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722280300, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722280426, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722280637, "dur": 577, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722281218, "dur": 572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722281801, "dur": 535, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722282342, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722282923, "dur": 541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722283475, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722284087, "dur": 615, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722284707, "dur": 654, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722285365, "dur": 3423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722288788, "dur": 3088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722291876, "dur": 2494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722294370, "dur": 2767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722297138, "dur": 2443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722299582, "dur": 2672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722302254, "dur": 2796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722305050, "dur": 3131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722308181, "dur": 2691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722310873, "dur": 2034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722313106, "dur": 653, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\UI\\Tree\\TreeHeaderSettings.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754045722312907, "dur": 3163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722316071, "dur": 2569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754045722318641, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722261489, "dur": 12692, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722274186, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_612DCBB3052D864A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722274829, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722275065, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_FADE1643CA5C8800.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722275221, "dur": 552, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722275780, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_ED2D3EB1C97F3A5F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722276422, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722276892, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_347DC0FF07300132.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722277240, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722277840, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_0E440929134BF079.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722278139, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722278453, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_48A7353BAEF13316.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722278718, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722279092, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_13697A979EB0EF82.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722279523, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722279600, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_AC876CD17C0FF1C1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754045722279726, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722280172, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722280250, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722280332, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722280418, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722280593, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722281193, "dur": 578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722281784, "dur": 546, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722282334, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722282912, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722283463, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722284090, "dur": 632, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722284732, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722285368, "dur": 3184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722288553, "dur": 2856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722291409, "dur": 3323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722294732, "dur": 2571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722297303, "dur": 2455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722299758, "dur": 2742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722302500, "dur": 2402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722304903, "dur": 2841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722307745, "dur": 2575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722310320, "dur": 2552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722313654, "dur": 565, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\ApplyShelveWithConflictsQuestionerBuilder.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754045722312872, "dur": 3341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722316214, "dur": 2349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754045722318623, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722261435, "dur": 12719, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722274162, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_C9F914A74E2B16B8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722274801, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722274989, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1CFBC647265B7D61.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722275214, "dur": 686, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722275904, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B40E848BC76ACCC7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722276506, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722276750, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B16C90EDBDE4DF78.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722276979, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722277515, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_66EAD504DFBBEDF3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722278025, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722278292, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_AB176FE8EEF474FE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722278643, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722278994, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_FD894A3BC08E0559.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722279388, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722279516, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722279656, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754045722279813, "dur": 523, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722280342, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722280524, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722280933, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722281476, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722281999, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722282521, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722283064, "dur": 526, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722283601, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722284193, "dur": 778, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722284995, "dur": 444, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722285444, "dur": 3139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722288584, "dur": 2799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722291383, "dur": 2719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722294103, "dur": 3197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722297301, "dur": 2225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722299527, "dur": 2809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722302337, "dur": 2010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722304347, "dur": 2792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722307139, "dur": 3057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722310196, "dur": 2821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722313614, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\UI\\Progress\\DrawProgressForOperations.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754045722313017, "dur": 3131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722316149, "dur": 2362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754045722318627, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722261479, "dur": 12687, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722274175, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_F279AD6DB3287D52.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722274806, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722274976, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_9919525412EAD2E7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722275194, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722275772, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_17A99C1F49AC544C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722276347, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722276531, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_CAEDB152A2C80D92.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722276935, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722277512, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8FAB570D0B1C8DCD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722278030, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722278296, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_1E7AA3722A1C93AD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722278638, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722278990, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_86405D0799B6BB11.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722279353, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722279425, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722279495, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722279606, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722279707, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722279935, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722280039, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_A45CE29EBBBE7D91.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754045722280161, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722280631, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754045722280681, "dur": 611, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722281296, "dur": 546, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722281854, "dur": 584, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722282445, "dur": 553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722283005, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722283547, "dur": 615, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722284190, "dur": 625, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722284839, "dur": 591, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722285434, "dur": 3208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722288642, "dur": 3096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722291738, "dur": 3503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722295241, "dur": 3169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722298411, "dur": 2341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722300752, "dur": 2477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722303229, "dur": 2689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722305918, "dur": 2593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722308511, "dur": 2392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722310903, "dur": 2546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722313449, "dur": 2220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722315670, "dur": 2481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754045722318151, "dur": 3560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722261520, "dur": 12673, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722274199, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_36C5F3CCEBE3CD07.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722274901, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722275302, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_E0A580A67B9BA514.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722275403, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722275888, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DB41EC179EB56DBA.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722276452, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722276569, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FCA09B517E031ABB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722276933, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722277485, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_14260DC7C6A62D51.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722277947, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722278231, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_885799F0F6E77EBF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722278638, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722278984, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_9FDB4296FD5402ED.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722279298, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722279390, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722279479, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722279555, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_7DC540854D1BBB01.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722279676, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722279913, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722280016, "dur": 603, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722280626, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754045722280690, "dur": 635, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722281331, "dur": 28173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754045722309505, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722309890, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722310058, "dur": 2695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722312754, "dur": 2465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722315227, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722316004, "dur": 2481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754045722318485, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722261571, "dur": 12632, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722274208, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_2202EAFAEF1B6C1F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722274908, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722275380, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_825F83770C6FBB2D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722275523, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722275925, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_054B6FF5C640A1AD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722276525, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722276932, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_30E83EFD1EA5B11D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722277381, "dur": 489, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722277875, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_51F15FE6673D9942.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722278169, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722278477, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_574D2D579249D9A8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722278728, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722279108, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_B359FB15FAFC6B04.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722279683, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722279920, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722280023, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_220A2C8EABC83166.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754045722280160, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722280648, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754045722280700, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722281334, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722281903, "dur": 537, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722282447, "dur": 604, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722283055, "dur": 511, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722283586, "dur": 576, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722284196, "dur": 632, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722284846, "dur": 651, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722285502, "dur": 3339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722291167, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.performance.profile-analyzer@a68e7bc84997\\Editor\\DepthSliceDropdown.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754045722288841, "dur": 2974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722291815, "dur": 2821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722294637, "dur": 2689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722297326, "dur": 2778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722300104, "dur": 2600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722304399, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticFunctionInvoker_1.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754045722302704, "dur": 2755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722305459, "dur": 2925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722308385, "dur": 2338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722310723, "dur": 2359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722313082, "dur": 3228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722316310, "dur": 2406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754045722318716, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722261706, "dur": 12513, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722274225, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_61250575CD81DD6E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722274929, "dur": 563, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722275501, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_57991EBFA1A26EC2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722275864, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722275963, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_521EBAF2E01427CC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722276568, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722277057, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_2B292166361825BD.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722277525, "dur": 494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722278023, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_4BCB4A18FFEC0881.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722278372, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722278676, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_A982C66D2D348ED7.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722279010, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722279196, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E6F1BAA280F85427.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754045722279841, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722280418, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722280650, "dur": 574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722281230, "dur": 596, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722281837, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722282378, "dur": 564, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722282951, "dur": 523, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722283490, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722284105, "dur": 625, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722284735, "dur": 654, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722288044, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\treeview\\TrackGui\\TimelineTrackGUI.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754045722285394, "dur": 3758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722289152, "dur": 2632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722291785, "dur": 3035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722294820, "dur": 3358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722298178, "dur": 2432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722300611, "dur": 2526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722303137, "dur": 3445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722306582, "dur": 3483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722310065, "dur": 2175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722312241, "dur": 2757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722314998, "dur": 2671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754045722317669, "dur": 4113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722261749, "dur": 12481, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722274235, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_9C0B6EFD81BDE75D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722274935, "dur": 584, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722275524, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_59BEFA3164F84BCE.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722275908, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722276042, "dur": 644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_E742B87826F4607B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722276686, "dur": 741, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722277430, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_F4C63E5BAEA27403.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722277909, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722278156, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_968B718F611B56F9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722278533, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722278798, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_386811273654939A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722279185, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722279304, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_CB3E21EA52206828.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754045722279899, "dur": 611, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722280520, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722280819, "dur": 616, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722281441, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722281932, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722282501, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722283062, "dur": 503, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722283612, "dur": 633, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722284251, "dur": 722, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722284980, "dur": 3403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722288383, "dur": 3183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722291567, "dur": 3851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722295418, "dur": 2812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722298230, "dur": 2842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722301073, "dur": 2666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722303739, "dur": 3375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722308427, "dur": 918, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@ad30dfd93d36\\Runtime\\TMP\\TMP_InputValidator.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754045722307115, "dur": 4110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722311226, "dur": 2626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722313852, "dur": 2334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722316187, "dur": 2255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754045722318443, "dur": 2818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722261794, "dur": 12447, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722274246, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_9E563BCFB42744CB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722274929, "dur": 559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722275497, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_CA47B9E89BF3744B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722275747, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722275913, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_98ABF5EF82F6C903.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722276525, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722276979, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_6A2198A2D22CCFAF.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722277513, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722277894, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E3304A047EE08E0B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722278290, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722278648, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_092522C3C590FAD2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722278995, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722279200, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9582B39E166EA023.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754045722279867, "dur": 637, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722280519, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722280946, "dur": 616, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722281568, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722282076, "dur": 517, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722282599, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722283081, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722283620, "dur": 570, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722284195, "dur": 667, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722284886, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722285465, "dur": 3125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722288591, "dur": 2706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722291298, "dur": 3041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722294339, "dur": 2685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722297024, "dur": 2442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722299466, "dur": 2560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722302027, "dur": 2201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722304228, "dur": 2806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722307035, "dur": 2942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722309980, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722310254, "dur": 2119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722312373, "dur": 2621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754045722314994, "dur": 3775, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722261823, "dur": 12427, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722274255, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_F22C2CAEEE3C250C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722274954, "dur": 574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722275533, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_E9025F1EBE6B906B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722275911, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722276046, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_8E5076AB386A4F5F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722276576, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722277068, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_86942DFBA173BD2F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722277544, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722277902, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_2E0DBB4CDB1259B3.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722278236, "dur": 404, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722278644, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_8C95FB58FA13BAB6.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722278995, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722279185, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_94172ADB7F54A7D8.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754045722279816, "dur": 521, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722280350, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722280511, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754045722280564, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722281135, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722281796, "dur": 571, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722282373, "dur": 563, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722282941, "dur": 531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722283496, "dur": 649, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722284175, "dur": 635, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722284813, "dur": 617, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722285437, "dur": 2948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722288385, "dur": 3463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722291848, "dur": 2866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722294715, "dur": 2900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722297615, "dur": 2826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722300442, "dur": 2743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722303186, "dur": 2720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722305906, "dur": 2363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722308270, "dur": 2681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722310952, "dur": 2620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722313573, "dur": 2442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722316015, "dur": 2670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754045722318685, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722261875, "dur": 12385, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722274260, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_610A92F577D74C23.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722274960, "dur": 614, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722275577, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_8B4EA0E95E9C463B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722275986, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722276070, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_F538D3A612872E43.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722276649, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722277146, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_EBEA4291B5996ADA.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722277522, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722277885, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_61B9003D7A1B656E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722278184, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722278533, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A2ECA603E2EEF8E4.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722278844, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722279101, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_23CB3E72B95C6251.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722279553, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722279688, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754045722279895, "dur": 639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722280538, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722281084, "dur": 667, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722281759, "dur": 535, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722282319, "dur": 515, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722282840, "dur": 497, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722283342, "dur": 546, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722283914, "dur": 555, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722284474, "dur": 665, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722285142, "dur": 3451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722288593, "dur": 3298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722291891, "dur": 3281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722295172, "dur": 3116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722298288, "dur": 2556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722300845, "dur": 2969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722303815, "dur": 3170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722306985, "dur": 2937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722309925, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722310041, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754045722310111, "dur": 2700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722312811, "dur": 2401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722315229, "dur": 2431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754045722317660, "dur": 4056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754045722326794, "dur": 1034, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 17020, "tid": 504, "ts": 1754045722951993, "dur": 1431, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 17020, "tid": 504, "ts": 1754045722955415, "dur": 27, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 17020, "tid": 504, "ts": 1754045722955824, "dur": 14, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 17020, "tid": 504, "ts": 1754045722953522, "dur": 1890, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 17020, "tid": 504, "ts": 1754045722955535, "dur": 288, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 17020, "tid": 504, "ts": 1754045722955907, "dur": 568, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 17020, "tid": 504, "ts": 1754045722948735, "dur": 8452, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}